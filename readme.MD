# Run with docker  

```
docker build . -t ovv-viewer

docker run -p80:8080 --name ovv-viewer ovv-viewer
```

# Configuration

override configuration of app and jdbc
create file <.war name>.xml in conf/Catalina/localhost

add following content
```
<?xml version="1.0" encoding="UTF-8"?>
<Context>
    <Environment name="ovvViewerConfig" value="file:d:\\tools\\server\\tomcat\\instances\\ovv-viewer-billa-9\\conf\\app.properties" type="java.lang.String"/>
</Context>
```

logging configuration override by copy log4j2.xml to 
lib folder
                             
## Run Database locally

It is possible to use docker-compose to run database locally.
1. Go to directory `tools/postgre-in-docker-compose`
2. use command `docker-compose up`
3. edit values in`src/main/resources/jdbc.properties`
```properties
jdbc.driver = org.postgresql.Driver
jdbc.url = ******************************************
jdbc.username = ovv
jdbc.password = pass
jpa.database.platform = org.hibernate.dialect.PostgreSQL91Dialect
```

