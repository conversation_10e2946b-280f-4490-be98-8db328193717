Changelog OVV Viewer
1.0.0
- New Features
    - Added filter Manual Redemption on the Transaction List page
    - Added filter Offline on the Transaction List page
    - Updated amount for Reversal Transactions
0.6.5
- Bugfixing
    - Fixed default time in FROM - TO filters (now  00:00:00 instead of current time, possible to change)
    - Added transaction result code filter: AMOUNT_DEF_NOT_FOUND
    - Fixed amount on Transaction Detail page (synchronized with displayed amount on Transaction List page)
    - Added Training Mode to Transaction Detail page (was missing)
0.6.4
- Bugfixing
    - Fixed default value for training mode in export files
0.6.3
- Bugfixing
    - Transaction filter contained wrong issuer name 'ERZEBETH', fixed to 'ERZSEBET'
    - Fixed redemption date on Manual Redemption page
0.6.2
- Bugfixing
    - Fixed SK message for wrong voucher state when manual redemption
    - Added numeric sorting of cash registers on the manual redemption page
0.6.1
- Bugfixing
    - Updated message when manual redemption with result wrong_voucher_state from TVM (i.e. voucher was accepted in TVM) (VOUCHERMAN-787)
    - Updated date time messages related to ovv date time (VOUCHERMAN-786)
0.6.0
- New Features
  - Transactions Section
    - Added training transaction flag and related filter (including xlsx export update) (VOUCHERMAN-769)
    - Added redemption date column (including xlsx export update) (VOUCHERMAN-767)
  - User Management
    - Import all users from TVM (CR 4, VOUCHERMAN-763)
  - Manual Redemption
    - Import checkout places on a daily basis (CR 3, VOUCHERMAN-771)
- Bugfixing
  - Transactions Section
    - Add missing columns into xlsx export files (which are displayed on web page, but missing inside the exported file, VOUCHERMAN-766)
    - Fix failing xlsx exports due to over limit (but actually not over the limit, VOUCHERMAN-770)
    - Required filter from - to is now optional only (VOUCHERMAN-776)
    - Fixed showing number of displayed rows on the current page (VOUCHERMAN-781)
  - Manual Redemption
    - Fixed when reversals have zero amount (VOUCHERMAN-772)
0.5.0
- VOUCHERMAN-747 Country code management for users
- VOUCHERMAN-748 Automatic voucher amount resolution
- VOUCHERMAN-749 Added manual redemption flag to transaction request
- VOUCHERMAN-755 Fixed wrong date time in xlsx files
- VOUCHERMAN-757 Deleted closed stores (PL and SK)
0.4.2
- Manual Redemption - corrected displayed fields for Reversal type
- Fixed some tranlations
- Force password change for the first login (for users created by admin or manager)
- Manual Redemption - corrected shown message in a case of communication error to OVV
0.4.1
- Double notification fixed on Manual Redemption page
- Some internal refactors (DB scripts)
0.4.0
- Users see transactions from all stores
- Added missing cost centre on the Batch detail page
- Ability to change your own password
- Force password change
- Manual Redemption - added redemption date
- Manual Redemption - reversal removed for non-admin users
- Users may have multiple stores
- Transactions - added support for new response codes
- Transactions - added expiration filter
- Bugfixing
    - Added missing batch role in the user edit section
    - Added missing successful response on the Batch Detail page
    - Fixed warning when manual redemption
    - Added date time from and to restrictions
    - Fixed translations

