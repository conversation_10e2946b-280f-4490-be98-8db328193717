jdbc.driver = org.h2.Driver
jdbc.url = jdbc:h2:mem:db;DB_CLOSE_DELAY=-1
jdbc.username = sa
jdbc.password = sa
jpa.database.platform = org.hibernate.dialect.H2Dialect
jpa.database.generateDdl=true

# If this is a number greater than 0, the application will test all idle,
# pooled but unchecked-out connections, every this number of seconds.
ds.connectionTestPeriod = 60
# Query to be executed for connection tests
ds.testQuery = select 1
# Minimum number of Connections a pool will maintain at any given time.
ds.minPoolSize = 5
# Maximum number of Connections a pool will maintain at any given time.
ds.maxPoolSize = 30
# Seconds a Connection can remain pooled but unused before being discarded.
ds.maxIdleTime = 120
