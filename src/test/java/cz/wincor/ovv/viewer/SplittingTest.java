package cz.wincor.ovv.viewer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Spliterator;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;


import cz.wincor.ovv.viewer.pojo.CashRegisterImportEntry;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class SplittingTest {
    @Test
    public void testSplitting() {

        Iterator<CashRegisterImportEntry> sourceIterator = Arrays.asList(new CashRegisterImportEntry("A", "B"),
                new CashRegisterImportEntry("A", "B"), new CashRegisterImportEntry("A", "B"),
                new CashRegisterImportEntry("A", "B"), new CashRegisterImportEntry("A", "B"),
                new CashRegisterImportEntry("A", "B"), new CashRegisterImportEntry("A", "B")).iterator();

        Iterable<CashRegisterImportEntry> iterable = () -> sourceIterator;
        Stream<CashRegisterImportEntry> importEntriesStream = StreamSupport.stream(iterable.spliterator(), false);

        Spliterator<CashRegisterImportEntry> split = importEntriesStream.spliterator();
        int batchSize = 2;
        int batchCount = 0;
        while (true) {
            List<CashRegisterImportEntry> batch = new ArrayList<>(batchSize);
            for (int i = 0; i < batchSize && split.tryAdvance(batch::add); i++) {
            }
            ;
            if (batch.isEmpty())
                break;
            batchCount++;
        }
        assertEquals(4, batchCount);
    }
}
