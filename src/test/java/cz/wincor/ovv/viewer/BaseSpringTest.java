package cz.wincor.ovv.viewer;

import cz.wincor.ovv.viewer.bootstrap.AppConfig;
import cz.wincor.ovv.viewer.bootstrap.DataSourceConfig;
import cz.wincor.ovv.viewer.bootstrap.JpaConfig;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.web.WebAppConfiguration;

@ExtendWith(SpringExtension.class)
@WebAppConfiguration
@ContextConfiguration(classes = {
        AppConfig.class,
        DataSourceConfig.class,
        JpaConfig.class})
public abstract class BaseSpringTest {
}
