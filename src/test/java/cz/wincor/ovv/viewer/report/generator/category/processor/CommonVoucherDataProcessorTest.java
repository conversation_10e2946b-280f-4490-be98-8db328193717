package cz.wincor.ovv.viewer.report.generator.category.processor;

import static cz.wincor.ovv.viewer.report.generator.TestDataGenerator.generateTestingVouchers;
import static cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil.findByCheckoutAndCategory;
import static cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil.getVolumeCounterByIssuer;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cz.wincor.ovv.viewer.report.generator.category.vo.CategoryAndCheckoutStatistic;
import cz.wincor.ovv.viewer.report.generator.category.vo.IssuerVolumeCounter;

public class CommonVoucherDataProcessorTest {

    private static final Logger logger = LoggerFactory.getLogger(CommonVoucherDataProcessorTest.class);

    private CommonVoucherDataProcessor commonVoucherDataProcessor;

    @BeforeEach
    public void init() {
        commonVoucherDataProcessor = new CommonVoucherDataProcessor();
    }

    @Test
    public void testProcessGlobalStatistics() {
        // Test method
        generateTestingVouchers().forEach(it -> commonVoucherDataProcessor.process(it));

        // Validate output data
        Map<String, List<IssuerVolumeCounter>> globalCategoryStatistics = commonVoucherDataProcessor.getGlobalCategoryStatistics();
        logger.info("Global statistics: {}", globalCategoryStatistics);
        assertEquals(3, globalCategoryStatistics.size());
        List<IssuerVolumeCounter> gifts = globalCategoryStatistics.get("GIFT");
        List<IssuerVolumeCounter> meals = globalCategoryStatistics.get("MEAL");
        assertEquals(3, gifts.size());
        assertEquals(3, meals.size());
        IssuerVolumeCounter sodexo = meals.stream().filter(it -> it.getIssuer().equals("SODEXO")).findFirst().get();
        assertEquals(1945, sodexo.getAmount());
        assertEquals(2, sodexo.getCount());
    }

    @Test
    public void testProcessDailyStatistics() {
        // Test method
        generateTestingVouchers().forEach(it -> commonVoucherDataProcessor.process(it));

        // Validate output data
        Map<LocalDate, List<CategoryAndCheckoutStatistic>> statistics = commonVoucherDataProcessor.getDailyCategoryStatistics();
        assertEquals(2, statistics.size());
        LocalDate localDate = LocalDate.of(2019, 12, 10);
        List<CategoryAndCheckoutStatistic> categoryAndCheckoutStatistics = statistics.get(localDate);
        assertNotNull(categoryAndCheckoutStatistics);
        assertEquals(4, categoryAndCheckoutStatistics.size());
        CategoryAndCheckoutStatistic meal = findByCheckoutAndCategory("1", "MEAL", categoryAndCheckoutStatistics).get();
        List<IssuerVolumeCounter> volumeCounters = meal.getVolumeCounters();
        assertEquals(2, volumeCounters.size());
        IssuerVolumeCounter sodexo = getVolumeCounterByIssuer("SODEXO", volumeCounters).get();
        assertEquals(2, sodexo.getCount());
        assertEquals(1945, sodexo.getAmount());
    }


}