package cz.wincor.ovv.viewer.report.generator;

import com.querydsl.core.types.Predicate;
import cz.wincor.ovv.viewer.BaseSpringTest;
import cz.wincor.ovv.viewer.model.ReportType;
import cz.wincor.ovv.viewer.model.entity.Voucher;
import cz.wincor.ovv.viewer.report.generator.category.DailyPartReportGenerator;
import cz.wincor.ovv.viewer.report.generator.category.GlobalPartReportGenerator;
import cz.wincor.ovv.viewer.repository.StoreRepository;
import cz.wincor.ovv.viewer.repository.VoucherRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static cz.wincor.ovv.viewer.report.generator.TestDataGenerator.generateTestingStore;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * Integration test generating full category report
 */
@ExtendWith(MockitoExtension.class)
public class CategoryReportGeneratorTest extends BaseSpringTest {

    private static final Logger logger = LoggerFactory.getLogger(CategoryReportGenerator.class);

    @Autowired
    @InjectMocks
    private CategoryReportGenerator categoryReportGenerator;

    @Autowired
    @InjectMocks
    private GlobalPartReportGenerator globalPartReportGenerator;

    @Autowired
    @InjectMocks
    private DailyPartReportGenerator dailyPartReportGenerator;

    @Mock
    private StoreRepository storeRepository;

    @Mock
    private VoucherRepository voucherRepository;

    @BeforeEach
    public void setup() {
        when(storeRepository.findById(anyLong())).thenReturn(Optional.of(generateTestingStore()));

        when(voucherRepository.findAll(any(Predicate.class), any(Pageable.class))).thenAnswer(new Answer() {
            private int count = 0;

            public Object answer(InvocationOnMock invocation) {
                if (++count == 1) {
                    List<Voucher> vouchers = TestDataGenerator.generateTestingVouchers();
                    return new PageImpl(vouchers, Pageable.unpaged(), vouchers.size());
                }
                return new PageImpl(Collections.emptyList(), Pageable.unpaged(), 0);
            }
        });
    }


    @Test
    public void generateAllReports() throws IOException {
        byte[] bytes = categoryReportGenerator.generateReport(TestDataGenerator.generateReport(ReportType.CATEGORY));
        assertNotNull(bytes);
        Path category = Files.createTempFile("CATEGORY", ".xlsx");
        Files.write(category, bytes);
        logger.info("Generated file {}", category);

    }

}