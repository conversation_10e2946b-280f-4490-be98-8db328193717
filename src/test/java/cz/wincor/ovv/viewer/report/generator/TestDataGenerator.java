package cz.wincor.ovv.viewer.report.generator;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.model.ReportType;
import cz.wincor.ovv.viewer.model.entity.Report;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.model.entity.Transaction;
import cz.wincor.ovv.viewer.model.entity.Voucher;

public class TestDataGenerator {

    public static List<Voucher> generateTestingVouchers() {
        List<Voucher> result = new ArrayList<>();
        LocalDateTime dateTime = LocalDateTime.of(LocalDate.of(2019, 12, 10), LocalTime.now());
        result.add(generateVoucher("MEAL", "SODEXO", 145, "1", dateTime));
        result.add(generateVoucher(null, "SODEXO", 145, "9", dateTime));
        result.add(generateVoucher("MEAL", "TVM", 787, "1", dateTime));
        result.add(generateVoucher("GIFT", "TVM", 7888, "1", dateTime));
        result.add(generateVoucher("MEAL", "SODEXO", 1800, "1", dateTime));
        result.add(generateVoucher("GIFT", "SODEXO", 10000, "2", dateTime));
        result.add(generateVoucher("GIFT", "FandF", 10000, "1", dateTime));
        dateTime = LocalDateTime.of(LocalDate.of(2019, 12, 11), LocalTime.now());
        result.add(generateVoucher("MEAL", "LCHD", 155, "2", dateTime));
        return result;
    }

    public static Voucher generateVoucher(String category, String issuer, long amount, String checkout, LocalDateTime dateTime) {
        Voucher voucher = new Voucher();
        voucher.setServerLocalDateTime(dateTime);
        Transaction transaction = new Transaction();
        transaction.setAmount(amount);
        transaction.setCategory(category);
        transaction.setOvvIssuer(issuer);
        transaction.setDeviceId(checkout);
        voucher.setRelatedTransactionRequest(transaction);
        return voucher;
    }

    public static Report generateReport(ReportType reportType) {
        Report report = new Report();
        report.setParams("{\"fromDate\":\"2019-07-01\",\"toDate\":\"2019-07-25\",\"storeId\":639}");
        report.setType(reportType);
        return report;
    }

    public static Store generateTestingStore() {
        Store store = new Store();
        store.setName("Testing store");
        store.setSiteCode("123456");
        store.setCountryCode(CountryEnum.CZ);
        store.setCostCentre("1234");
        return store;
    }

}
