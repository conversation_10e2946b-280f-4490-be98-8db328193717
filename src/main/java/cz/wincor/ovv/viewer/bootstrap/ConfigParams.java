package cz.wincor.ovv.viewer.bootstrap;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;

/**
 * Application configuration parameters.
 *
 * <AUTHOR>
 */
public class ConfigParams {

    @Value("${ovv.transactionSearch.maximalPeriod:180}")
    private int transactionSearchTimeLimit;

    @Value("${app.version}")
    private String appVersion;

    @Value("${sftp.host}")
    private String sftpHost;

    @Value("${sftp.port}")
    private Integer sftpPort;

    @Value("${sftp.username}")
    private String sftpUsername;

    @Value("${sftp.password}")
    private String sftpPassword;

    @Value("${sftp.privateKey}")
    private String sftpPrivateKey;

    @Value("${sftp.privateKeyPassphrase}")
    private String sftpPrivateKeyPassphrase;

    @Value("${ovv.ui.dateFormat}")
    private String dateFormat;

    @Value("${app.export.maxSize}")
    private int maxExportSize;

    @Value("${app.export.batchSize}")
    private int exportBatchSize;

    @Value("${app.report.maxDaysRange}")
    private int reportMaxDaysRange;

    /**
     * Perform additional validation of configured values.
     */
    @PostConstruct
    public void validateOptions() {

    }

    public int getTransactionSearchTimeLimit() {
        return transactionSearchTimeLimit;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public String getSftpHost() {
        return sftpHost;
    }

    public Integer getSftpPort() {
        return sftpPort;
    }

    public String getSftpUsername() {
        return sftpUsername;
    }

    public String getSftpPassword() {
        return sftpPassword;
    }

    public String getSftpPrivateKey() {
        return sftpPrivateKey;
    }

    public String getSftpPrivateKeyPassphrase() {
        return sftpPrivateKeyPassphrase;
    }

    public String getDateFormat() {
        return dateFormat;
    }

    public int getMaxExportSize() {
        return maxExportSize;
    }

    public int getExportBatchSize() {
        return exportBatchSize;
    }

    public int getReportMaxDaysRange() {
        return reportMaxDaysRange;
    }
}
