package cz.wincor.ovv.viewer.bootstrap;

import com.jcraft.jsch.JSchException;
import cz.wincor.ovv.viewer.utils.sftp.SftpClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.HashMap;
import java.util.Map;

@Configuration
@ComponentScan("cz.wincor.ovv.viewer")
@PropertySource("classpath:app.properties")
@EnableScheduling
public class AppConfig {
    private static final Logger LOG = LoggerFactory.getLogger(AppConfig.class);

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public Jaxb2Marshaller getMarshaller() {
        Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
        jaxb2Marshaller.setPackagesToScan("cz.wincor.ovv.viewer.xsd");
        Map<String, Object> map = new HashMap<>();
        map.put(jakarta.xml.bind.Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);

        jaxb2Marshaller.setMarshallerProperties(map);
        return jaxb2Marshaller;
    }

    @Bean
    public PropertySourcesPlaceholderConfigurer propertyConfigurer() {
        PropertySourcesPlaceholderConfigurer propertyConfigurer = new PropertySourcesPlaceholderConfigurer();
        propertyConfigurer.setTrimValues(true);
        return propertyConfigurer;
    }

    @Bean
    public ConfigParams configParams() {
        return new ConfigParams();
    }

    @Bean
    public Factory<SftpClient> sftpClientFactory() {
        return () -> {
            try {
                ConfigParams config = configParams();
                if (StringUtils.isNotBlank(config.getSftpPassword())) {
                    return new SftpClient(config.getSftpHost(), config.getSftpPort(), config.getSftpUsername(),
                            config.getSftpPassword());
                }
                if (StringUtils.isNotBlank(config.getSftpPrivateKey())) {
                    return new SftpClient(config.getSftpHost(), config.getSftpPort(), config.getSftpUsername(),
                            config.getSftpPrivateKey(), config.getSftpPrivateKeyPassphrase());
                } else {
                    throw new IllegalStateException();
                }

            } catch (JSchException e) {
                LOG.error("Unable to create SFTP connection", e);
                throw new IllegalStateException(e);
            }
        };

    }

    public interface Factory<T> {
        T getObject();
    }
}
