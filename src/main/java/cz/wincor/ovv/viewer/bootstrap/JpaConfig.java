package cz.wincor.ovv.viewer.bootstrap;

import cz.wincor.ovv.viewer.model.entity.User;
import cz.wincor.ovv.viewer.security.SecurityUtils;
import jakarta.persistence.EntityManagerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.support.TransactionTemplate;

import javax.sql.DataSource;
import java.util.Optional;

/**
 * Spring Data JPA configuration.
 *
 * <AUTHOR>
 */
@Configuration
@EnableJpaRepositories(
//        repositoryBaseClass = SliceableRepositoryImpl.class,
        basePackages = "cz.wincor.ovv.viewer.repository",
        enableDefaultTransactions = false)
@EnableTransactionManagement
@EnableJpaAuditing
public class JpaConfig {

    @Value("${jpa.showSql:false}")
    private boolean showSql;

    @Value("${jpa.database.generateDdl:false}")
    private boolean generateDdl;

    @Autowired
    private DataSource dataSource;


    @Bean
    public LocalContainerEntityManagerFactoryBean entityManagerFactory() {
        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setShowSql(showSql);
        vendorAdapter.setGenerateDdl(generateDdl);

        LocalContainerEntityManagerFactoryBean factory = new LocalContainerEntityManagerFactoryBean();
        factory.setJpaVendorAdapter(vendorAdapter);
        factory.setDataSource(dataSource);
        factory.setPackagesToScan("cz.wincor.ovv.viewer.model.entity");
        factory.setEntityManagerFactoryInterface(EntityManagerFactory.class);
        return factory;
    }

    @Bean
    public PlatformTransactionManager transactionManager() {
        JpaTransactionManager txManager = new JpaTransactionManager();
        txManager.setEntityManagerFactory(entityManagerFactory().getObject());
        return txManager;
    }

    @Bean
    public TransactionTemplate transactionTemplate() {
        return new TransactionTemplate(transactionManager());
    }

    @Bean
    public AuditorAware<User> userAuditorAware() {
        return () -> {
            if (SecurityUtils.isAuthenticated()) {
                return Optional.of(SecurityUtils.getLoggedUser());
            }
            return Optional.empty();
        };
    }
}
