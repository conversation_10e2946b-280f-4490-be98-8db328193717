package cz.wincor.ovv.viewer.bootstrap;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import cz.wincor.base.spring.context.support.SerializableResourceBundleMessageSource;
import cz.wincor.ovv.viewer.controller.CommonModelAttrsInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.format.FormatterRegistry;
import org.springframework.http.CacheControl;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.ViewResolverRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.resource.VersionResourceResolver;
import org.thymeleaf.extras.springsecurity6.dialect.SpringSecurityDialect;
import org.thymeleaf.spring6.SpringTemplateEngine;
import org.thymeleaf.spring6.templateresolver.SpringResourceTemplateResolver;
import org.thymeleaf.spring6.view.ThymeleafViewResolver;
import org.thymeleaf.templatemode.TemplateMode;
import org.thymeleaf.templateresolver.ITemplateResolver;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Year;
import java.util.List;

@Configuration
@EnableWebMvc
@ComponentScan("cz.wincor.ovv.viewer.controller")
public class WebAppConfig implements WebMvcConfigurer  {

    private static final int CACHE_PERIOD = 365 * 24 * 60 * 60;

    @Autowired
    private ConfigParams config;

    @Autowired
    private ApplicationContext applicationContext;


    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        // TODO: replace this trailing slash deprecated config with filter when Spring 6.2 is out
        // https://github.com/spring-projects/spring-framework/issues/31366
        configurer.setUseTrailingSlashMatch(true);

        configurer.setPathMatcher(new AntPathMatcher());
    }


    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        ObjectMapper json = Jackson2ObjectMapperBuilder
                .json()
                .featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .build().registerModule(new JavaTimeModule());
        converters.add(new MappingJackson2HttpMessageConverter(json));
        converters.add(new ByteArrayHttpMessageConverter());
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(new Converter<String, LocalDate>() {
            @Override
            public LocalDate convert(String source) {
                if (StringUtils.isBlank(source)) {
                    return null;
                }
                return LocalDate.parse(source);
            }
        });
        registry.addConverter(new Converter<String, LocalDateTime>() {
            @Override
            public LocalDateTime convert(String source) {
                if (StringUtils.isBlank(source)) {
                    return null;
                }
                return LocalDateTime.parse(source);
            }
        });
        registry.addConverter(new Converter<String, Year>() {
            @Override
            public Year convert(String source) {
                if (StringUtils.isBlank(source)) {
                    return null;
                }
                return Year.parse(source);
            }
        });
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addViewController("/login").setViewName("login");
        registry.addViewController("/").setViewName("index");
        registry.addViewController("/index.html").setViewName("index");
        registry.addViewController("/navbar.html").setViewName("navbar");
        registry.addViewController("/sidebar.html").setViewName("sidebar");
        registry.addViewController("/export-failed.html").setViewName("export-failed");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new CommonModelAttrsInterceptor(config.getAppVersion()))
                .addPathPatterns("/navbar.html", "/sidebar.html", "/login**");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // CSS resources from /resources
        registry.addResourceHandler("/resources/css/**")
                .addResourceLocations("/resources/css/")
                .setCachePeriod(CACHE_PERIOD)
                .resourceChain(false) // FIXME pro produkci musi byt caching zapnuty!
                .addResolver(new VersionResourceResolver()
                        .addContentVersionStrategy("/**"));
        // All other resources from /resources
        registry.addResourceHandler("/resources/**").addResourceLocations("/resources/");
        // JS resources from /app -> cached and versioned by content
        registry.addResourceHandler("/app/**/*.js")
                .addResourceLocations("/app/")
                .setCachePeriod(CACHE_PERIOD)
                .resourceChain(false) // FIXME pro produkci musi byt caching zapnuty!
                .addResolver(new VersionResourceResolver()
                        .addContentVersionStrategy("/**/*.js"));
        // HTML resources from /app
        registry.addResourceHandler("/app/**/*.html").addResourceLocations("/app/")
                .setCacheControl(CacheControl.noCache());
    }


    //~ Thymeleaf view resolving

    @Bean
    public SpringTemplateEngine templateEngine() {
        SpringTemplateEngine templateEngine = new SpringTemplateEngine();
        templateEngine.setTemplateResolver(templateResolver());
        templateEngine.addDialect(new SpringSecurityDialect());
        return templateEngine;
    }

    private ITemplateResolver templateResolver() {
        SpringResourceTemplateResolver resolver = new SpringResourceTemplateResolver();
        resolver.setApplicationContext(applicationContext);
        resolver.setPrefix("/WEB-INF/templates/");
        resolver.setSuffix(".html");
        resolver.setTemplateMode(TemplateMode.HTML);
        resolver.setCacheTTLMs(2000l);
        return resolver;
    }

    @Override
    public void configureViewResolvers(ViewResolverRegistry registry) {
        ThymeleafViewResolver viewResolver = new ThymeleafViewResolver();
        viewResolver.setTemplateEngine(templateEngine());
        viewResolver.setCharacterEncoding("UTF-8");
        viewResolver.setOrder(1);
        registry.viewResolver(viewResolver);
    }

    @Bean
    public SerializableResourceBundleMessageSource messageSource() {
        SerializableResourceBundleMessageSource source = new SerializableResourceBundleMessageSource();
        source.setBasename("classpath:l10n/messages");
        source.setUseCodeAsDefaultMessage(true);
        return source;
    }
}
