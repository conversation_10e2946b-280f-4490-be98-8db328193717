package cz.wincor.ovv.viewer.bootstrap;

import cz.wincor.ovv.viewer.bootstrap.filter.MDCUserFilter;
import jakarta.servlet.DispatcherType;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterRegistration;
import jakarta.servlet.ServletContainerInitializer;
import jakarta.servlet.ServletContext;
import jakarta.servlet.ServletException;
import jakarta.servlet.SessionCookieConfig;
import org.springframework.data.web.config.SpringDataWebConfiguration;
import org.springframework.web.SpringServletContainerInitializer;
import org.springframework.web.filter.CharacterEncodingFilter;
import org.springframework.web.filter.DelegatingFilterProxy;
import org.springframework.web.servlet.resource.ResourceUrlEncodingFilter;
import org.springframework.web.servlet.support.AbstractAnnotationConfigDispatcherServletInitializer;

import java.util.EnumSet;

/**
 * Web application initializer, handled by standard {@link ServletContainerInitializer} via Spring's
 * {@link SpringServletContainerInitializer}. Replaces old web.xml approach.
 *
 * <AUTHOR>
 */
public class OvvViewerAppInitializer extends AbstractAnnotationConfigDispatcherServletInitializer {

    @Override
    protected Class<?>[] getRootConfigClasses() {
        return new Class[]{
                AppConfig.class,
                DataSourceConfig.class,
                JpaConfig.class,
                SecurityConfig.class};
    }

    @Override
    protected Class<?>[] getServletConfigClasses() {
        return new Class[]{
                WebAppConfig.class,
                SpringDataWebConfiguration.class};
    }

    @Override
    protected String[] getServletMappings() {
        return new String[]{"/"};
    }

    @Override
    protected Filter[] getServletFilters() {
        return new Filter[]{
                new MDCUserFilter()
        };
    }

    @Override
    public void onStartup(ServletContext servletContext) throws ServletException {
        super.onStartup(servletContext);

        // Configure session cookie
        SessionCookieConfig cookieConfig = servletContext.getSessionCookieConfig();
        cookieConfig.setName("ovvViewerSessionId");
        cookieConfig.setHttpOnly(true);

        // Filters, all mapped to /*
        EnumSet<DispatcherType> dispatchers = EnumSet.of(
                DispatcherType.REQUEST, DispatcherType.FORWARD, DispatcherType.INCLUDE, DispatcherType.ASYNC);
        FilterRegistration.Dynamic registration = servletContext.addFilter("characterEncodingFilter", new CharacterEncodingFilter("UTF-8"));
        registration.addMappingForUrlPatterns(dispatchers, true, "/*");

        registration = servletContext.addFilter("springSecurityFilter", new DelegatingFilterProxy("springSecurityFilterChain"));
        registration.addMappingForUrlPatterns(dispatchers, true, "/*");

        registration = servletContext.addFilter("resourceUrlEncodingFilter", new ResourceUrlEncodingFilter());
        registration.addMappingForUrlPatterns(dispatchers, true, "/*");
    }
}
