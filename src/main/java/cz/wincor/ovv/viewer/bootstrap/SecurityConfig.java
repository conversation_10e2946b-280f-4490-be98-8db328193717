package cz.wincor.ovv.viewer.bootstrap;

import cz.wincor.base.spring.security.CsrfCookieFilter;
import cz.wincor.ovv.viewer.model.entity.User;
import cz.wincor.ovv.viewer.security.LogoutEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.authentication.DefaultAuthenticationEventPublisher;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.HttpStatusEntryPoint;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.security.web.csrf.CsrfFilter;
import org.springframework.security.web.csrf.CsrfTokenRepository;
import org.springframework.security.web.csrf.HttpSessionCsrfTokenRepository;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import java.util.HashMap;

/**
 * Spring Security configuration class.
 *
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true)
public class SecurityConfig {

    private CsrfTokenRepository csrfTokenRepository = createCsrfTokenRepository();

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    public void configureGlobal(AuthenticationManagerBuilder auth) throws Exception {
        auth.authenticationEventPublisher(new DefaultAuthenticationEventPublisher(publisher));
    }

    /**
     * Data API specific security configuration.
     */
    @Bean
    @Order(1)
    public SecurityFilterChain apiFilterChain(HttpSecurity http) throws Exception {
        http
                .securityMatcher("/api/**")
                .httpBasic(AbstractHttpConfigurer::disable)
                .logout(AbstractHttpConfigurer::disable)
                .anonymous(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(req -> req.anyRequest().authenticated())
                .csrf(csrf -> csrf.csrfTokenRepository(csrfTokenRepository))
                .headers(headers -> headers.cacheControl(Customizer.withDefaults()))
                .addFilterAfter(new CsrfCookieFilter(), CsrfFilter.class)
                .exceptionHandling(eh -> eh.authenticationEntryPoint(new HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED)));
        return http.build();
    }


    /**
     * Security configuration for common web resources.
     */
    @Bean
    @Order(2)
    public SecurityFilterChain webResourcesFilterChain(HttpSecurity http) throws Exception {
        http
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers("/login**", "/error*", "/changePassword/**").permitAll()
                        .requestMatchers("/resources/**", "/app/**").permitAll()
                        .anyRequest().authenticated()
                )
                .logout(logout -> logout
                        .logoutRequestMatcher(new AntPathRequestMatcher("/logout", "GET"))
                        .logoutSuccessHandler(getLogoutSuccessHandler())
                        .permitAll()
                        .invalidateHttpSession(true)
                )
                .httpBasic(AbstractHttpConfigurer::disable)
                .csrf(csrf -> csrf.csrfTokenRepository(csrfTokenRepository))
                .addFilterAfter(new CsrfCookieFilter(), CsrfFilter.class)
                .exceptionHandling(eh -> eh.accessDeniedPage("/error403"))
                .headers(headers -> headers.cacheControl(Customizer.withDefaults()))
                .formLogin(formLogin -> {
                    formLogin.failureHandler(expiredPasswordAuthFailureHandler());
                    formLogin.loginPage("/login");
                    formLogin.usernameParameter("username");
                    formLogin.passwordParameter("password");
                    formLogin.loginProcessingUrl("/login");
                    formLogin.defaultSuccessUrl("/index.html");
                    formLogin.permitAll();
                });
        return http.build();
    }

    @Bean
    public ApplicationListener applicationListener() {
        return new OvvApplicationListener();
    }

    /**
     * Create common CSRF token repository shared by both API and Web Resources.
     */
    private CsrfTokenRepository createCsrfTokenRepository() {
        HttpSessionCsrfTokenRepository repository = new HttpSessionCsrfTokenRepository();
        // Angular sends CSRF tokens in this HTTP header
        repository.setHeaderName("X-XSRF-TOKEN");
        return repository;
    }

    private LogoutSuccessHandler getLogoutSuccessHandler() {
        return (request, response, authentication) -> {
            String username = ((User) authentication.getPrincipal()).getUsername();
            response.setStatus(HttpStatus.OK.value());
            response.sendRedirect(request.getContextPath() + "/login?logout=true");
            publisher.publishEvent(new LogoutEvent(authentication, "Logged out."));
        };
    }

    private static AuthenticationFailureHandler expiredPasswordAuthFailureHandler() {
        ForwardingAuthenticationFailureHandler handler = new ForwardingAuthenticationFailureHandler();
        HashMap<String, ForwardingAuthenticationFailureHandler.FailureUrl> failureUrlMap = new HashMap<>();

        ForwardingAuthenticationFailureHandler.FailureUrl failureUrl2 = handler.new FailureUrl("/changePassword", false);
        failureUrlMap.put(AccountExpiredException.class.getName(), failureUrl2);
        handler.setExceptionMappings(failureUrlMap);
        handler.setDefaultFailureUrl("/login?error=true");
        return handler;
    }
}
