package cz.wincor.ovv.viewer.bootstrap;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.annotation.PropertySources;

import com.mchange.v2.c3p0.ComboPooledDataSource;

/**
 * DataSource configuration.
 *
 * <AUTHOR>
 */
@Configuration
@PropertySources({
    @PropertySource("classpath:jdbc.properties"),
    @PropertySource(value = "${ovvViewerConfig}", ignoreResourceNotFound = true)
})
public class DataSourceConfig {

    //~ Data source properties

    @Value("${jdbc.driver}")
    private String jdbcDriver;

    @Value("${jdbc.url}")
    private String jdbcUrl;

    @Value("${jdbc.username}")
    private String jdbcUsername;

    @Value("${jdbc.password}")
    private String jdbcPassword;

    @Value("${ds.connectionTestPeriod}")
    private int connectionTestPeriod;

    @Value("${ds.testQuery}")
    private String testQuery;

    @Value("${ds.minPoolSize}")
    private int minPoolSize;

    @Value("${ds.maxPoolSize}")
    private int maxPoolSize;

    @Value("${ds.maxIdleTime}")
    private int maxIdleTime;


    @Bean
    public DataSource dataSource() {
        ComboPooledDataSource cpds = new ComboPooledDataSource();
        try {
            // Load the JDBC driver
            Class.forName(jdbcDriver);
            cpds.setDriverClass(jdbcDriver);
        } catch (Exception e) {
            throw new RuntimeException("Failed to load JDBC driver " + jdbcDriver + ".", e);
        }
        cpds.setJdbcUrl(jdbcUrl);
        cpds.setUser(jdbcUsername);
        cpds.setPassword(jdbcPassword);
        cpds.setTestConnectionOnCheckin(true);
        // best performance
        cpds.setTestConnectionOnCheckout(false);
        cpds.setIdleConnectionTestPeriod(connectionTestPeriod);
        cpds.setPreferredTestQuery(testQuery);
        cpds.setInitialPoolSize(minPoolSize);
        cpds.setMinPoolSize(minPoolSize);
        cpds.setMaxPoolSize(maxPoolSize);
        cpds.setMaxIdleTime(maxIdleTime);
        return cpds;
    }
}
