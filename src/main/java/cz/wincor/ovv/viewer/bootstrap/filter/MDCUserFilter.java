package cz.wincor.ovv.viewer.bootstrap.filter;

import cz.wincor.ovv.viewer.model.entity.User;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import org.slf4j.MDC;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.io.IOException;

public class MDCUserFilter implements jakarta.servlet.Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String userInfo = (authentication != null && authentication.getPrincipal() instanceof User)
                ? ((User) authentication.getPrincipal()).getUsername() : "NA";
        MDC.put("user", userInfo);
        try {
            chain.doFilter(request, response);
        } finally {
            MDC.remove("user");
        }
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void destroy() {
    }

}
