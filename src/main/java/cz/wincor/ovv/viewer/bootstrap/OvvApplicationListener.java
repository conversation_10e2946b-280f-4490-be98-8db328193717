package cz.wincor.ovv.viewer.bootstrap;

import cz.wincor.ovv.viewer.dto.UserDto;
import cz.wincor.ovv.viewer.model.entity.User;
import cz.wincor.ovv.viewer.security.*;
import cz.wincor.ovv.viewer.utils.SyslogAudit;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.event.AuthenticationFailureBadCredentialsEvent;
import org.springframework.security.authentication.event.InteractiveAuthenticationSuccessEvent;

/**
 * Application listerner for all syslog audit events.
 */
public class OvvApplicationListener implements ApplicationListener {
    @Override
    public void onApplicationEvent(ApplicationEvent applicationEvent) {
        if (applicationEvent instanceof InteractiveAuthenticationSuccessEvent) {
            InteractiveAuthenticationSuccessEvent event = (InteractiveAuthenticationSuccessEvent) applicationEvent;
            String username = ((User) ((UsernamePasswordAuthenticationToken) event.getSource()).getPrincipal()).getUsername();
            SyslogAudit.audit(SyslogAudit.Event.LOGIN_FAIL, "cz.wincor.ovv.viewer.bootstrap.SecurityConfig", "User: " + username + " logged in.");
        }

        if (applicationEvent instanceof AuthenticationFailureBadCredentialsEvent) {
            AuthenticationFailureBadCredentialsEvent event = (AuthenticationFailureBadCredentialsEvent) applicationEvent;
            SyslogAudit.audit(SyslogAudit.Event.LOGIN_FAIL, "cz.wincor.ovv.viewer.bootstrap.SecurityConfig", "Bad Credentials entered.");
        }
        if (applicationEvent instanceof LogoutEvent) {
            LogoutEvent event = (LogoutEvent) applicationEvent;
            String username = ((User) ((UsernamePasswordAuthenticationToken) event.getSource()).getPrincipal()).getUsername();
            SyslogAudit.audit(SyslogAudit.Event.LOGOUT, "cz.wincor.ovv.viewer.bootstrap.SecurityConfig", "User: " + username + " logged out.");
        }

        if (applicationEvent instanceof UserCreatedEvent) {
            UserCreatedEvent event = (UserCreatedEvent) applicationEvent;
            SyslogAudit.audit(SyslogAudit.Event.CREDENTIALSMANIPULATION_INSERT, "cz.wincor.ovv.viewer.service.impl.UserServiceImpl", "User: " + event.getSource() + " has been added to the system.");
        }
        if (applicationEvent instanceof UserPasswordChangedEvent) {
            UserPasswordChangedEvent event = (UserPasswordChangedEvent) applicationEvent;
            SyslogAudit.audit(SyslogAudit.Event.CREDENTIALSMANIPULATION_UPDATE, "cz.wincor.ovv.viewer.service.impl.UserServiceImpl", "User: " + event.getSource() + " has changed password.");
        }
        if (applicationEvent instanceof UserRoleUpdated) {
            UserRoleUpdated event = (UserRoleUpdated) applicationEvent;
            SyslogAudit.audit(SyslogAudit.Event.CREDENTIALSMANIPULATION_UPDATE, "cz.wincor.ovv.viewer.service.impl.UserServiceImpl", "User: " + event.getSource() + " role updated " + event.getOldRole().name() + " -> " + event.getNewRole().name() + ".");
        }
        if (applicationEvent instanceof UserStatusChangedEvent) {
            UserStatusChangedEvent event = (UserStatusChangedEvent) applicationEvent;
            SyslogAudit.audit(SyslogAudit.Event.CREDENTIALSMANIPULATION_UPDATE, "cz.wincor.ovv.viewer.service.impl.UserServiceImpl", "User: " +  event.getSource() + " has been " + (event.isNewStatus() ? "enabled" : "disabled") + ".");
        }


    }
}
