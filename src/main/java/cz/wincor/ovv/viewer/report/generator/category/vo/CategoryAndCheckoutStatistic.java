package cz.wincor.ovv.viewer.report.generator.category.vo;

import java.util.ArrayList;
import java.util.List;

public class CategoryAndCheckoutStatistic {
    private String category;
    private String checkout;
    private List<IssuerVolumeCounter> volumeCounters = new ArrayList<>();

    public CategoryAndCheckoutStatistic(String category, String checkout) {
        this.category = category;
        this.checkout = checkout;
    }

    public String getCheckout() {
        return checkout;
    }

    public String getCategory() {
        return category;
    }

    public List<IssuerVolumeCounter> getVolumeCounters() {
        return volumeCounters;
    }

    public boolean isEmpty() {
        return volumeCounters.isEmpty();
    }


}

