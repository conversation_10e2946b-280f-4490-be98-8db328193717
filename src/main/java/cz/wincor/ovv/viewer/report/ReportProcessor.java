package cz.wincor.ovv.viewer.report;

import cz.wincor.ovv.viewer.bootstrap.ConfigParams;
import cz.wincor.ovv.viewer.model.ReportType;
import cz.wincor.ovv.viewer.model.entity.Report;
import cz.wincor.ovv.viewer.repository.ReportRepository;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.apache.poi.util.TempFile;
import org.apache.poi.util.TempFileCreationStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * Component responsible for asynchronous processing of submitted reports. Every submitted
 * {@link Report} gets enqueued by this processor and the processor allocates a worker thread
 * to process the report, i.e. generate the report content.
 *
 * <AUTHOR>
 */
@Component
public class ReportProcessor implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(ReportProcessor.class);

    // Poison pill used to stop the main processing thread
    private static final Report POISON_PILL = new Report();

    /**
     * Maximum number of pending reports in the queue for processing.
     */
    @Value("${reports.queueSize}")
    private int queueSize;
    /**
     * Number of concurrent threads processing the report queue.
     */
    @Value("${reports.threads}")
    private int threads;
    /**
     * Location for temp file generated by POI
     */
    @Value("${java.io.tmpdir}")
    private String tempLocation;

    @Autowired
    private ReportRepository reportRepository;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private ReportGenerator[] reportGenerators;
    @Autowired
    private ConfigParams config;

    private BlockingQueue<Report> queue;
    private ExecutorService executorService;
    private Map<ReportType, ReportGenerator> generatorMap;


    @PostConstruct
    public void init() {
        logger.info("Initializing report processor: queueSize={}, threads={}.", queueSize, threads);
        this.queue = new ArrayBlockingQueue<>(queueSize);
        this.executorService = Executors.newFixedThreadPool(threads);
        this.generatorMap = Arrays.stream(reportGenerators)
                .collect(Collectors.toMap(rg -> rg.getSupportedReportType(), rg -> rg));

        File poiTempFileDirectory = new File(tempLocation, "ovv-poifiles");

        TempFile.setTempFileCreationStrategy(new TempFileCreationStrategy() {
            @Override
            public File createTempFile(String prefix, String suffix) throws IOException {
                // check dir exists, make if doesn't
                if (!poiTempFileDirectory.exists()) {
                    poiTempFileDirectory.mkdir();
                    poiTempFileDirectory.deleteOnExit();
                }
                File newFile = File.createTempFile(prefix, suffix, poiTempFileDirectory);
                return newFile;
            }

            @Override
            public File createTempDirectory(String s) throws IOException {
                // check dir exists, make if doesn't
                if (!poiTempFileDirectory.exists()) {
                    poiTempFileDirectory.mkdir();
                    poiTempFileDirectory.deleteOnExit();
                }
                return Files.createTempDirectory(poiTempFileDirectory.toPath(), s + "-").toFile();
            }
        });
    }

    /**
     * Submit the specified report for processing, i.e. add it to the end of the queue
     * of reports for processing.
     * @param report Unprocessed report to be enqueued. Never {@code null}.
     */
    public void submit(Report report) {
        logger.info("Enqueuing {} for processing.", report);
        queue.add(report);
    }


    @Override
    public void run() {
        logger.info("Starting up report processing, number of pending reports: {}.", queue.size());

        while (true) {
            try {
                Report report = queue.take();
                if (POISON_PILL == report) {
                    break;
                }
                ReportGenerator generator = generatorMap.get(report.getType());
                executorService.execute(new ReportProcessorWorker(
                        report, generator, reportRepository, transactionTemplate));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        logger.info("Report processing stopped.");
    }

    @PreDestroy
    public void shutdown() {
        queue.clear();
        queue.add(POISON_PILL);
        executorService.shutdown();
    }
}
