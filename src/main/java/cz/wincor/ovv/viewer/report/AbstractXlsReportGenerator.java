package cz.wincor.ovv.viewer.report;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.springframework.core.io.Resource;

import cz.wincor.ovv.viewer.dto.ReportParams;
import cz.wincor.ovv.viewer.model.entity.Report;


public abstract class AbstractXlsReportGenerator implements ReportGenerator {

    public static final DateTimeFormatter GENERATED_TIME_FORMAT = DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm:ss");
    public static final DateTimeFormatter PARAM_DATE_FORMAT = DateTimeFormatter.ofPattern("dd.MM.yyyy");

    // JSON (de)serializer of report parameters
    private ReportParamsSerializer reportParamsSerializer = new ReportParamsSerializer();

    protected abstract Resource getXlsTemplate();

    protected boolean shouldUseBorders() {
        return false;
    }


    @Override
    public byte[] generateReport(Report report) {
        XlsReportWorkbook workbook = new XlsReportWorkbook(getXlsTemplate(), shouldUseBorders());
        ReportParams reportParams = reportParamsSerializer.deserialize(report.getParams());

        // Write headers
        writeReportParams(reportParams, workbook);

        // Transform xls to stream workbook
        workbook.transformToStream();
        // Write data
        writeData(report, reportParams, workbook);

        // Finalize

        // Get output
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            throw new RuntimeException("An error occurred while writing report content.", e);
        }
        return outputStream.toByteArray();
    }

    protected void writeGenerationTime(XlsReportWorkbook workbook) {
        String label = workbook.getValue(0, 1);
        workbook.writeValue(0, 1, label + LocalDateTime.now().format(GENERATED_TIME_FORMAT));
    }

    protected void writeReportParams(ReportParams reportParams, XlsReportWorkbook workbook) {
    }

    protected abstract void writeData(Report report, ReportParams params, XlsReportWorkbook workbook);

}
