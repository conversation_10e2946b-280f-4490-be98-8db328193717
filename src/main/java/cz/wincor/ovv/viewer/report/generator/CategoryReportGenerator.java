package cz.wincor.ovv.viewer.report.generator;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import cz.wincor.ovv.viewer.bootstrap.ConfigParams;
import cz.wincor.ovv.viewer.dto.ReportParams;
import cz.wincor.ovv.viewer.exception.LargeExportException;
import cz.wincor.ovv.viewer.model.OvvVoucherState;
import cz.wincor.ovv.viewer.model.ReportType;
import cz.wincor.ovv.viewer.model.entity.QVoucher;
import cz.wincor.ovv.viewer.model.entity.Report;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.model.entity.Voucher;
import cz.wincor.ovv.viewer.report.AbstractXlsReportGenerator;
import cz.wincor.ovv.viewer.report.XlsReportWorkbook;
import cz.wincor.ovv.viewer.report.generator.category.DailyPartReportGenerator;
import cz.wincor.ovv.viewer.report.generator.category.GlobalPartReportGenerator;
import cz.wincor.ovv.viewer.report.generator.category.processor.CommonVoucherDataProcessor;
import cz.wincor.ovv.viewer.repository.StoreRepository;
import cz.wincor.ovv.viewer.repository.VoucherRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * The class responsible for generating Category report
 * <AUTHOR>
 */
@Component
public class CategoryReportGenerator extends AbstractXlsReportGenerator {

    private static final Logger logger = LoggerFactory.getLogger(CategoryReportGenerator.class);

    @Value("classpath:xls/CategoryReportTemplate.xlsx")
    private Resource xlsTemplate;

    @Autowired
    private VoucherRepository voucherRepository;

    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private ConfigParams configParams;

    @Autowired
    private GlobalPartReportGenerator globalPartReportGenerator;

    @Autowired
    private DailyPartReportGenerator dailyPartReportGenerator;

    @Override
    public ReportType getSupportedReportType() {
        return ReportType.CATEGORY;
    }

    @Override
    protected Resource getXlsTemplate() {
        return xlsTemplate;
    }

    @Override
    protected void writeData(Report report, ReportParams params, XlsReportWorkbook workbook) {
        Predicate predicate = createPredicate(params);
        int page = 0;
        int totalCount = 0;
        CommonVoucherDataProcessor commonVoucherDataProcessor = new CommonVoucherDataProcessor();
        Page<Voucher> vouchers = voucherRepository.findAll(predicate, PageRequest.of(page, configParams.getExportBatchSize()));
        do {
            logger.info("Processing next {} vouchers in report id {}, page {}", vouchers.getNumberOfElements(), report.getId(), page);
            totalCount = totalCount + vouchers.getNumberOfElements();
            if (totalCount > configParams.getMaxExportSize()) {
                throw new LargeExportException("Maximum export size is " + configParams.getMaxExportSize());
            }
            vouchers.getContent().forEach(it -> commonVoucherDataProcessor.process(it));
            vouchers = voucherRepository.findAll(predicate, PageRequest.of(++page, configParams.getExportBatchSize()));
        } while (vouchers.hasContent());
        globalPartReportGenerator.writeOneSheetReport(params, workbook, commonVoucherDataProcessor);
        logger.info("The first report sheet generated in report id {}", report.getId());
        workbook.switchSheet(1);
        dailyPartReportGenerator.writeOneSheetReport(params, workbook, commonVoucherDataProcessor);
        logger.info("The second report sheet generated in report id {}", report.getId());
    }


    private Predicate createPredicate(ReportParams params) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (params.getFromDate() != null) {
            booleanBuilder.and(QVoucher.voucher.serverLocalDateTime.after(LocalDateTime.of(params.getFromDate(), LocalTime.MIDNIGHT)));
        }

        if (params.getToDate() != null) {
            LocalDateTime finalTo;
            if (params.getToDate().isBefore(LocalDate.now())) {
                finalTo = LocalDateTime.of(params.getToDate(), LocalTime.MIDNIGHT).plusDays(1);
            } else {
                //To avoid replication delay problems
                finalTo = LocalDateTime.of(params.getToDate(), LocalTime.now().minusMinutes(5));
            }
            booleanBuilder.and(QVoucher.voucher.serverLocalDateTime.before(finalTo));
        }

        if (params.getStoreId() != null) {
            Store store = findStore(params.getStoreId());
            booleanBuilder.and(QVoucher.voucher.relatedTransactionRequest.paymentPlace.eq(store.getSiteCode()));
            booleanBuilder.and(QVoucher.voucher.relatedTransactionRequest.countryCode.eq(store.getCountryCode().name()));
        }

        booleanBuilder.and(QVoucher.voucher.voucherState.eq(OvvVoucherState.USED));

        return booleanBuilder.getValue();
    }

    private Store findStore(long id) {
        return storeRepository.findById(id).get();
    }

    @Override
    protected boolean shouldUseBorders() {
        return true;
    }
}
