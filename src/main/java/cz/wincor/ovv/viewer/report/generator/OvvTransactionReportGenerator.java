package cz.wincor.ovv.viewer.report.generator;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import cz.wincor.ovv.viewer.bootstrap.ConfigParams;
import cz.wincor.ovv.viewer.dto.ReportParams;
import cz.wincor.ovv.viewer.exception.LargeExportException;
import cz.wincor.ovv.viewer.model.OvvVoucherState;
import cz.wincor.ovv.viewer.model.ReportType;
import cz.wincor.ovv.viewer.model.entity.QVoucher;
import cz.wincor.ovv.viewer.model.entity.Report;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.model.entity.Voucher;
import cz.wincor.ovv.viewer.report.AbstractXlsReportGenerator;
import cz.wincor.ovv.viewer.report.XlsReportWorkbook;
import cz.wincor.ovv.viewer.repository.StoreRepository;
import cz.wincor.ovv.viewer.repository.VoucherRepository;
import cz.wincor.ovv.viewer.utils.PropertyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;


@Component
public class OvvTransactionReportGenerator extends AbstractXlsReportGenerator {

    private static final Logger logger = LoggerFactory.getLogger(OvvTransactionReportGenerator.class);
    
    private static final BigDecimal HUNDRED = new BigDecimal(100);

    @Value("classpath:xls/UsedVouchersTemplate.xlsx")
    private Resource xlsTemplate;

    @Autowired
    private VoucherRepository voucherRepository;

    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private ConfigParams configParams;

    @Override
    public ReportType getSupportedReportType() {
        return ReportType.USED_VOUCHERS;
    }

    @Override
    protected Resource getXlsTemplate() {
        return xlsTemplate;
    }

    @Override
    protected void writeReportParams(ReportParams reportParams, XlsReportWorkbook workbook) {
        if (reportParams == null) {
            // Reporting filter not defined
            return;
        }

        String filterTemplate = workbook.getValue(0, 0);

        if (reportParams.getStoreId() != null) {
            Store store = storeRepository.findById(reportParams.getStoreId()).get();
            filterTemplate = filterTemplate.replaceFirst("\\{STORE\\}", store.getSiteCode() + " - " + store.getName());
        }


        String value = filterTemplate
                .replaceFirst("\\{FROM\\}", PARAM_DATE_FORMAT.format(reportParams.getFromDate()))
                .replaceFirst("\\{TO\\}", PARAM_DATE_FORMAT.format(reportParams.getToDate()))
                .replaceFirst("\\{GENERATED\\}", GENERATED_TIME_FORMAT.format(LocalDateTime.now()));


        workbook.writeValue(0, 0, value);
    }

    @Override
    protected void writeData(Report report, ReportParams params, XlsReportWorkbook workbook) {
        Predicate predicate = createPredicate(params);
        int page = 0;
        workbook.goToRow(2);
        int totalCount = 0;
        List<Voucher> vouchers = voucherRepository.getVouchers(predicate, PageRequest.of(page, configParams.getExportBatchSize()));
        do {
            logger.info("Processing next {} vouchers in report id {}, page {}", vouchers.size(), report.getId(), page);
            totalCount = totalCount + vouchers.size();
            if (totalCount > configParams.getMaxExportSize()) {
                throw new LargeExportException("Maximum export size is " + configParams.getMaxExportSize());
            }
            for (Voucher voucher : vouchers) {
                workbook.writeValue(0, voucher.getServerLocalDateTime().withNano(0));
                workbook.writeValue(8, voucher.getVoucherNumber());
                workbook.writeValue(2, voucher.getCountryCode().value());

                if (voucher.getRelatedTransactionRequest() != null) {
                    workbook.writeValue(1, voucher.getRelatedTransactionRequest().getDeviceDateTime().withNano(0));
                    workbook.writeValue(3, voucher.getRelatedTransactionRequest().getDeviceId());
                    workbook.writeValue(4, PropertyUtils.booleanToYesNo(voucher.getRelatedTransactionRequest().getManualRedemption()));
                    workbook.writeValue(5, voucher.getRelatedTransactionRequest().getStan());
                    workbook.writeValue(6, voucher.getRelatedTransactionRequest().getPaymentPlace());
                    long amount = voucher.getRelatedTransactionRequest().getAmount();
                    workbook.writeValue(7, new BigDecimal(amount).divide(HUNDRED));
                    workbook.writeValue(9, voucher.getRelatedTransactionRequest().getOvvIssuer());
                    workbook.writeValue(10, voucher.getRelatedTransactionRequest().getCategory());
                    workbook.writeValue(11, PropertyUtils.smallBooleanToYesNo(voucher.getRelatedTransactionRequest().isOfflineMode()));
                }
                workbook.nextRow();
            }
            vouchers = voucherRepository.getVouchers(predicate, PageRequest.of(++page, configParams.getExportBatchSize()));
        } while (vouchers.size() > 0);

    }

    private Predicate createPredicate(ReportParams params) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (params.getFromDate() != null) {
            booleanBuilder.and(QVoucher.voucher.serverLocalDateTime.after(LocalDateTime.of(params.getFromDate(), LocalTime.MIDNIGHT)));
        }

        if (params.getToDate() != null) {
            LocalDateTime finalTo;
            if (params.getToDate().isBefore(LocalDate.now())) {
                finalTo = LocalDateTime.of(params.getToDate(), LocalTime.MIDNIGHT).plusDays(1);
            } else {
                //To avoid replication delay problems
                finalTo = LocalDateTime.of(params.getToDate(), LocalTime.now().minusMinutes(5));
            }
            booleanBuilder.and(QVoucher.voucher.serverLocalDateTime.before(finalTo));
        }

        if (params.getStoreId() != null) {
            Store store = storeRepository.findById(params.getStoreId()).orElse(null);
            booleanBuilder.and(QVoucher.voucher.relatedTransactionRequest.paymentPlace.eq(store.getSiteCode()));
            booleanBuilder.and(QVoucher.voucher.relatedTransactionRequest.countryCode.eq(store.getCountryCode().name()));
        }

        booleanBuilder.and(QVoucher.voucher.voucherState.eq(OvvVoucherState.USED));

        return booleanBuilder.getValue();
    }


}
