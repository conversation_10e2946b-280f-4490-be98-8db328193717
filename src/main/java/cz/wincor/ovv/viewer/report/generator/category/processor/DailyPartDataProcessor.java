package cz.wincor.ovv.viewer.report.generator.category.processor;

import static cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil.findByCheckoutAndCategory;
import static cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil.getVolumeCounterByIssuer;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import cz.wincor.ovv.viewer.model.entity.Transaction;
import cz.wincor.ovv.viewer.model.entity.Voucher;
import cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil;
import cz.wincor.ovv.viewer.report.generator.category.vo.CategoryAndCheckoutStatistic;
import cz.wincor.ovv.viewer.report.generator.category.vo.IssuerVolumeCounter;

/**
 * The processor prepares data for the daily part of the report (the second sheet)
 *
 * <AUTHOR>
 */
public class DailyPartDataProcessor implements VoucherDataProcessor {

    private Map<LocalDate, List<CategoryAndCheckoutStatistic>> statistics = new TreeMap<>();
    private Set<String> tvmCategories = new HashSet<>();
    private Set<String> externalCategories = new HashSet<>();

    @Override
    public void process(Voucher voucher) {
        LocalDate date = voucher.getServerLocalDateTime().toLocalDate();
        if (!statistics.containsKey(date)) {
            statistics.put(date, new ArrayList<>());
        }
        analyzeCategories(voucher);
        processOneDay(statistics.get(date), voucher);
    }

    private void processOneDay(List<CategoryAndCheckoutStatistic> categoryAndCheckoutStatistics, Voucher voucher) {
        Transaction trnRequest = voucher.getRelatedTransactionRequest();
        CategoryAndCheckoutStatistic checkoutStats = findByCheckoutAndCategory(
                trnRequest.getDeviceId(), getCategory(trnRequest), categoryAndCheckoutStatistics).orElseGet(
                () -> {
                    CategoryAndCheckoutStatistic statistic = new CategoryAndCheckoutStatistic(getCategory(trnRequest), trnRequest.getDeviceId());
                    categoryAndCheckoutStatistics.add(statistic);
                    return statistic;
                });
        String ovvIssuer = trnRequest.getOvvIssuer();
        List<IssuerVolumeCounter> volumeCounters = checkoutStats.getVolumeCounters();
        IssuerVolumeCounter issuerVolumeCounter = getVolumeCounterByIssuer(ovvIssuer, volumeCounters).orElseGet(() -> {
            IssuerVolumeCounter ivc = new IssuerVolumeCounter(ovvIssuer);
            volumeCounters.add(ivc);
            return ivc;
        });
        issuerVolumeCounter.addSingleItem(trnRequest.getAmount());
    }

    private void analyzeCategories(Voucher voucher) {
        Transaction request = voucher.getRelatedTransactionRequest();
        String category = getCategory(request);
        if (CategoryReportUtil.ISSUER_TVM.equals(request.getOvvIssuer())) {
            tvmCategories.add(category);
        } else if (!CategoryReportUtil.ISSUER_FANDF.equals(request.getOvvIssuer())) {
            externalCategories.add(category);
        }
    }

    private String getCategory(Transaction request) {
        String category = request.getCategory();
        if (category == null) {
            category = CategoryReportUtil.CATEGORY_OTHER;
        }
        return category;
    }

    public Map<LocalDate, List<CategoryAndCheckoutStatistic>> getStatistics() {
        return Collections.unmodifiableMap(statistics);
    }

    public Set<String> getTvmCategories() {
        return tvmCategories;
    }

    public Set<String> getExternalCategories() {
        return externalCategories;
    }
}
