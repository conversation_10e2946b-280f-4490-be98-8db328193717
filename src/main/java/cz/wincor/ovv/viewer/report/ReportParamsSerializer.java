package cz.wincor.ovv.viewer.report;

import java.io.IOException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import cz.wincor.ovv.viewer.dto.ReportParams;

@Component
public class ReportParamsSerializer {

    // Jackson mapper for serialization and deserialization of JSON data
    private final ObjectMapper objectMapper;


    /**
     * Constructor initializing the underlying Jackson's {@link ObjectMapper}.
     */
    public ReportParamsSerializer() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModules(new JavaTimeModule());
        this.objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
    }

    public String serialize(ReportParams params) {
        try {
            return objectMapper.writeValueAsString(params);
        } catch (JsonProcessingException e) {
            throw new IllegalStateException("An error occurred while trying to serialize report parameters.", e);
        }
    }

    public ReportParams deserialize(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return objectMapper.readValue(value, ReportParams.class);
        } catch (IOException e) {
            throw new IllegalStateException("An error occurred while trying to parse "
                    + "report parameters: '" + value + "'.");
        }
    }
}
