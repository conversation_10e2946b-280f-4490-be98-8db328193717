package cz.wincor.ovv.viewer.report.generator.category;

import static cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cz.wincor.ovv.viewer.dto.ReportParams;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.report.XlsReportWorkbook;
import cz.wincor.ovv.viewer.report.generator.category.processor.CommonVoucherDataProcessor;
import cz.wincor.ovv.viewer.report.generator.category.vo.CategoryAndCheckoutStatistic;
import cz.wincor.ovv.viewer.report.generator.category.vo.IssuerVolumeCounter;
import cz.wincor.ovv.viewer.repository.StoreRepository;

/**
 * The class responsible for generating the second sheet in the category report
 * <AUTHOR>
 */
@Component
public class DailyPartReportGenerator implements OneSheetReportGenerator {

    @Autowired
    private StoreRepository storeRepository;

    @Override
    public void writeOneSheetReport(ReportParams params, XlsReportWorkbook workbook, CommonVoucherDataProcessor commonVoucherDataProcessor) {
        writeTableHeaders(params, workbook, commonVoucherDataProcessor);
        writeTableData(params, workbook, commonVoucherDataProcessor);
    }

    private void writeTableData(ReportParams params, XlsReportWorkbook workbook, CommonVoucherDataProcessor commonVoucherDataProcessor) {
        Map<LocalDate, List<CategoryAndCheckoutStatistic>> statistics = commonVoucherDataProcessor.getDailyCategoryStatistics();
        Set<String> uniqueExternalCategories = commonVoucherDataProcessor.getDailyReportUniqueExternalCategories();
        Set<String> uniqueTvmCategories = commonVoucherDataProcessor.getDailyReportUniqueTvmCategories();

        for (Map.Entry<LocalDate, List<CategoryAndCheckoutStatistic>> oneDayData : statistics.entrySet()) {
            Set<String> checkouts = getUniqueCheckouts(oneDayData.getValue());
            // Date column
            workbook.writeValue(0, checkouts.size(), 1, PARAM_DATE_FORMAT.format(oneDayData.getKey()));

            // Store column
            if (params.getStoreId() != null) {
                Store store = findStore(params.getStoreId());
                workbook.writeValue(1, checkouts.size(), 1, store.getSiteCode());
                workbook.writeValue(2, checkouts.size(), 1, store.getName());
            }


            // Write each checkout separately
            int column = 3;
            for (String checkout : checkouts) {
                workbook.writeValue(column, 0, 2, checkout);
                column = column + 2;
                // Process TVM
                IssuerVolumeCounter tvmCheckoutTotal = new IssuerVolumeCounter();
                for (String tvmCategory : uniqueTvmCategories) {
                    CategoryAndCheckoutStatistic oneRowStats = findByCheckoutAndCategory(checkout, tvmCategory, oneDayData.getValue())
                            .orElse(new CategoryAndCheckoutStatistic(tvmCategory, checkout));
                    IssuerVolumeCounter tvm = CategoryReportUtil.getVolumeCounterByIssuer(ISSUER_TVM, oneRowStats.getVolumeCounters())
                            .orElse(new IssuerVolumeCounter(ISSUER_TVM));
                    workbook.writeValue(column++, new BigDecimal(tvm.getAmount()).divide(HUNDRED));
                    tvmCheckoutTotal.add(tvm.getCount(), tvm.getAmount());
                }
                // Total TVM
                workbook.writeValue(column++, new BigDecimal(tvmCheckoutTotal.getAmount()).divide(HUNDRED));

                // Process FandF
                List<IssuerVolumeCounter> counters = findAllCountersByCheckout(checkout, oneDayData.getValue());
                IssuerVolumeCounter fandF = getVolumeCounterByIssuer(ISSUER_FANDF, counters)
                        .orElse(new IssuerVolumeCounter(ISSUER_FANDF));
                workbook.writeValue(column++, new BigDecimal(fandF.getAmount()).divide(HUNDRED));

                // External
                IssuerVolumeCounter externalCheckoutTotal = new IssuerVolumeCounter();
                for (String category : uniqueExternalCategories) {
                    CategoryAndCheckoutStatistic oneRowStats = findByCheckoutAndCategory(checkout, category, oneDayData.getValue())
                            .orElse(new CategoryAndCheckoutStatistic(category, checkout));
                    IssuerVolumeCounter external = CategoryReportUtil.getVolumeCounterExceptIssuers(oneRowStats.getVolumeCounters(), ISSUER_TVM, ISSUER_FANDF)
                            .orElse(new IssuerVolumeCounter());
                    workbook.writeValue(column++, new BigDecimal(external.getAmount()).divide(HUNDRED));
                    externalCheckoutTotal.add(external.getCount(), external.getAmount());
                }
                // Total External
                workbook.writeValue(column++, new BigDecimal(externalCheckoutTotal.getAmount()).divide(HUNDRED));

                // One row total (last column for each checkout)
                IssuerVolumeCounter oneRowTotal = sumAllCounters(tvmCheckoutTotal, fandF, externalCheckoutTotal);
                workbook.writeValue(column++, new BigDecimal(oneRowTotal.getAmount()).divide(HUNDRED));

                // Back to start
                column = 3;
                workbook.nextRow();
            }
            // Write total row
            writeOneDayTotalRow(workbook, uniqueExternalCategories, uniqueTvmCategories, oneDayData.getValue());

            workbook.nextRow();
        }

    }

    private void writeOneDayTotalRow(XlsReportWorkbook workbook, Set<String> uniqueExternalCategories, Set<String> uniqueTvmCategories, List<CategoryAndCheckoutStatistic> oneDayData) {
        int column;// Write one day total row
        column = 3;
        workbook.applyBoldStyle(workbook.writeValue(column, 0, 2, "Daily Total"));
        column = column + 2;

        // TVM daily total row
        IssuerVolumeCounter tvmDailyTotal = new IssuerVolumeCounter();
        for (String tvmCategory : uniqueTvmCategories) {
            CategoryAndCheckoutStatistic byCategory = findByCategory(tvmCategory, oneDayData)
                    .orElse(new CategoryAndCheckoutStatistic(tvmCategory, null));
            IssuerVolumeCounter tvmByCategory = getVolumeCounterByIssuer(ISSUER_TVM, byCategory.getVolumeCounters()).orElse(new IssuerVolumeCounter(ISSUER_TVM));
            workbook.applyBoldStyle(workbook.writeValue(column++, new BigDecimal(tvmByCategory.getAmount()).divide(HUNDRED)));
            tvmDailyTotal.add(tvmByCategory.getCount(), tvmByCategory.getAmount());
        }
        workbook.applyBoldStyle(workbook.writeValue(column++, new BigDecimal(tvmDailyTotal.getAmount()).divide(HUNDRED)));

        // FandF daily total row
        IssuerVolumeCounter fandFDailyTotal = getVolumeCounterByIssuerFromStatistics(ISSUER_FANDF, oneDayData);
        workbook.applyBoldStyle(workbook.writeValue(column++, new BigDecimal(fandFDailyTotal.getAmount()).divide(HUNDRED)));

        // External daily total row
        IssuerVolumeCounter externalDailyTotal = new IssuerVolumeCounter();
        for (String category : uniqueExternalCategories) {
            CategoryAndCheckoutStatistic byCategory = findByCategory(category, oneDayData)
                    .orElse(new CategoryAndCheckoutStatistic(category, null));
            IssuerVolumeCounter externalByCategory = getVolumeCounterExceptIssuers(byCategory.getVolumeCounters(), ISSUER_TVM, ISSUER_FANDF)
                    .orElse(new IssuerVolumeCounter());
            workbook.applyBoldStyle(workbook.writeValue(column++, new BigDecimal(externalByCategory.getAmount()).divide(HUNDRED)));
            externalDailyTotal.add(externalByCategory.getCount(), externalByCategory.getAmount());
        }
        workbook.applyBoldStyle(workbook.writeValue(column++, new BigDecimal(externalDailyTotal.getAmount()).divide(HUNDRED)));

        // Total all
        IssuerVolumeCounter dailyTotal = sumAllCounters(tvmDailyTotal, fandFDailyTotal, externalDailyTotal);
        workbook.applyBoldStyle(workbook.writeValue(column++, new BigDecimal(dailyTotal.getAmount()).divide(HUNDRED)));
    }

    private void writeTableHeaders(ReportParams params, XlsReportWorkbook workbook, CommonVoucherDataProcessor commonVoucherDataProcessor) {
        workbook.goToRow(1);
        Set<String> uniqueExternalCategories = commonVoucherDataProcessor.getDailyReportUniqueExternalCategories();
        Set<String> uniqueTvmCategories = commonVoucherDataProcessor.getDailyReportUniqueTvmCategories();
        Store store = null;
        if (params.getStoreId() != null) {
            store = findStore(params.getStoreId());
        }
        String info = "%s - %s / %s - %s (Generated: %s)";
        info = String.format(info,
                store.getSiteCode(),
                store.getName(),
                PARAM_DATE_FORMAT.format(params.getFromDate()),
                PARAM_DATE_FORMAT.format(params.getToDate()),
                GENERATED_TIME_FORMAT.format(LocalDateTime.now())
        );
        workbook.writeValue(0, 0, 9 + uniqueExternalCategories.size() + uniqueTvmCategories.size(), info);
        workbook.nextRow();

        workbook.applyHeaderStyle(workbook.writeValue(0, 2, 1, "Date"));
        workbook.applyHeaderStyle(workbook.writeValue(1, 2, 2, "Store"));
        workbook.applyHeaderStyle(workbook.writeValue(3, 2, 1, "Checkout"));
        workbook.applyHeaderStyle(workbook.writeValue(4, "Metrics"));
        workbook.applyHeaderStyle(workbook.writeValue(5, 0,
                4 + uniqueExternalCategories.size() + uniqueTvmCategories.size(), "Amount of redeemed vouchers"));
        workbook.nextRow();

        workbook.applyHeaderStyle(workbook.writeValue(4, "Issuer"));
        if (uniqueTvmCategories.isEmpty()) {
            workbook.applyHeaderStyle(workbook.writeValue(5, ISSUER_TVM));
        } else {
            workbook.applyHeaderStyle(workbook.writeValue(5, 0, 1 + uniqueTvmCategories.size(), ISSUER_TVM));
        }
        workbook.applyHeaderStyle(workbook.writeValue(5 + 1 + uniqueTvmCategories.size(), ""));
        workbook.applyHeaderStyle(workbook.writeValue(5 + 1 + uniqueTvmCategories.size() + 1, 0, 1 + uniqueExternalCategories.size(), "External"));
        workbook.applyHeaderStyle(workbook.writeValue(5 + 1 + uniqueTvmCategories.size() + 1 + uniqueExternalCategories.size() + 1, ""));
        workbook.nextRow();

        workbook.applyHeaderStyle(workbook.writeValue(4, "Kind of voucher", true));
        int column = 5;
        for (String category : uniqueTvmCategories) {
            workbook.applyHeaderStyle(workbook.writeValue(column++, category));
        }
        workbook.applyHeaderStyle(workbook.writeValue(column++, "Total TVM", true));

        workbook.applyHeaderStyle(workbook.writeValue(column++, ISSUER_FANDF));

        for (String category : uniqueExternalCategories) {
            workbook.applyHeaderStyle(workbook.writeValue(column++, category));
        }
        workbook.applyHeaderStyle(workbook.writeValue(column++, "Total External", true));

        workbook.applyHeaderStyle(workbook.writeValue(column++, "Total (All)", true));
        workbook.nextRow();


    }

    private Store findStore(long id) {
        return storeRepository.findById(id).orElse(null);
    }
}
