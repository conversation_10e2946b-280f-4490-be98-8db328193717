package cz.wincor.ovv.viewer.report;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Locale;

import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.DateFormatConverter;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;

/**
 * Wrapper around Apache POI's {@link HSSFWorkbook} handling common features of all XLS reports.
 *
 * <AUTHOR>
 */
public class XlsReportWorkbook {

    private static final Logger logger = LoggerFactory.getLogger(XlsReportWorkbook.class);

    private Workbook workbook;
    private Sheet sheet;

    private int rowIdx = 0;
    private Row row;

    private CellStyle amountStyle;
    private CellStyle rightAlignedStyle;
    private CellStyle dateStyle;
    private CellStyle dateTimeStyle;
    private CellStyle hyperlinkStyle;
    private CellStyle mergedStyle;
    private Font boldFont;
    private boolean useBorders;
    private CellStyle defaultStyle;

    public XlsReportWorkbook(Resource reportTemplate) {
        this(reportTemplate, false);
    }

    public XlsReportWorkbook(Resource reportTemplate, boolean useBorders) {
        try {
            this.workbook = WorkbookFactory.create(reportTemplate.getInputStream());
            this.sheet = workbook.getSheetAt(0);
            this.useBorders = useBorders;
            setCellStyles();
        } catch (EncryptedDocumentException | IOException e) {
            logger.error("Failed to create new XLS workbook from template {}.", reportTemplate);
            throw new RuntimeException("Error occured while creating XLS workbook from template.", e);
        }
    }

    public void switchSheet(int index) {
        this.sheet = workbook.getSheetAt(index);
    }

    public void transformToStream() {
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook((XSSFWorkbook) workbook);
        (sxssfWorkbook).setCompressTempFiles(true);
        SXSSFSheet sheet = sxssfWorkbook.getSheetAt(0);
        this.workbook = sxssfWorkbook;
        this.sheet = sheet;
    }

    public void setCellStyles() {
        defaultStyle = workbook.createCellStyle();
        defaultStyle.cloneStyleFrom(sheet.getColumnStyle(0));
        if (useBorders) {
            defaultStyle.setBorderBottom(BorderStyle.THIN);
            defaultStyle.setBorderTop(BorderStyle.THIN);
            defaultStyle.setBorderLeft(BorderStyle.THIN);
            defaultStyle.setBorderRight(BorderStyle.THIN);
        }

        // Create style for amounts
        DataFormat dataFormat = workbook.getCreationHelper().createDataFormat();
        this.amountStyle = workbook.createCellStyle();
        this.amountStyle.cloneStyleFrom(defaultStyle);
        this.amountStyle.setDataFormat(dataFormat.getFormat("#,##0.00"));

        // Create style for date
        DataFormat dateFormat = workbook.getCreationHelper().createDataFormat();
        this.dateStyle = workbook.createCellStyle();
        this.dateStyle.cloneStyleFrom(defaultStyle);
        this.dateStyle.setDataFormat(dateFormat.getFormat("dd.MM.yyyy"));

        // Create style for date time
        DataFormat dateTimeFormat = workbook.getCreationHelper().createDataFormat();
        this.dateTimeStyle = workbook.createCellStyle();
        this.dateTimeStyle.cloneStyleFrom(defaultStyle);
        this.dateTimeStyle.setDataFormat(dateTimeFormat.getFormat(DateFormatConverter.convert(Locale.getDefault(), "dd.MM.yyyy HH:mm:ss")));

        // Create style for hyperlinks
        this.hyperlinkStyle = workbook.createCellStyle();
        Font hyperlinkFont = workbook.createFont();
        hyperlinkFont.setUnderline(Font.U_SINGLE);
        hyperlinkFont.setColor(IndexedColors.BLUE.getIndex());
        this.hyperlinkStyle.cloneStyleFrom(defaultStyle);
        this.hyperlinkStyle.setFont(hyperlinkFont);

        // Create style for right aligned cell texts
        this.rightAlignedStyle = workbook.createCellStyle();
        this.rightAlignedStyle.cloneStyleFrom(defaultStyle);
        this.rightAlignedStyle.setAlignment(HorizontalAlignment.RIGHT);

        //Create style for merged cells
        this.mergedStyle = workbook.createCellStyle();
        mergedStyle.setAlignment(HorizontalAlignment.CENTER);
        mergedStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        mergedStyle.setWrapText(true);

        this.boldFont = workbook.createFont();
        boldFont.setBold(true);
    }

    public String getValue(int rowIdx, int colIdx) {
        Cell cell = getCell(rowIdx, colIdx);
        return cell.getStringCellValue();
    }

    private Cell getCell(int rowIdx, int colIdx) {
        Row row = sheet.getRow(rowIdx);
        if (row == null) {
            throw new IllegalArgumentException("Row with index " + rowIdx + " does not exist.");
        }
        Cell cell = row.getCell(colIdx);
        if (cell == null) {
            throw new IllegalArgumentException("Cell with index " + colIdx + " does not exist.");
        }
        return cell;
    }

    public Cell writeValue(int rowIdx, int colIdx, Object value) {
        Cell cell = getCell(rowIdx, colIdx);
        writeValue(cell, value);
        return cell;
    }

    public Cell writeValue(int colIdx, int rowSpan, int colSpan, Object value) {
        Cell cell = writeValue(colIdx, value);
        // No need to merge anything (exception otherwise)
        if (rowSpan == 0 && colSpan == 1) {
            return cell;
        }
        CellRangeAddress region = new CellRangeAddress(rowIdx, rowIdx + rowSpan, colIdx, (colIdx - 1) + colSpan);
        sheet.addMergedRegion(region);
        cell.setCellStyle(mergedStyle);
        RegionUtil.setBorderBottom(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderTop(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, region, sheet);
        return cell;
    }

    public void applyHeaderStyle(Cell cell) {
        CellStyle newCellSType = workbook.createCellStyle();
        newCellSType.cloneStyleFrom(cell.getCellStyle());
        newCellSType.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        newCellSType.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        newCellSType.setFont(boldFont);
        cell.setCellStyle(newCellSType);
    }

    public void applyBoldStyle(Cell cell) {
        CellStyle newCellSType = workbook.createCellStyle();
        newCellSType.cloneStyleFrom(cell.getCellStyle());
        newCellSType.setFont(boldFont);
        cell.setCellStyle(newCellSType);
    }

    public Cell writeValue(int colIdx, Object value) {
        Cell cell = row.getCell(colIdx);
        if (cell == null) {
            cell = row.createCell(colIdx);
        }
        return writeValue(cell, value);
    }

    public Cell writeValue(int colIdx, Object value, boolean wrapText) {
        Cell cell = row.getCell(colIdx);
        if (cell == null) {
            cell = row.createCell(colIdx);
        }
        CellStyle style = cell.getCellStyle();
        style.setWrapText(wrapText);
        return writeValue(cell, value);
    }

    private Cell writeValue(Cell cell, Object value) {
        if (value == null) {
            cell.setCellValue("");
            cell.setCellStyle(defaultStyle);
        } else if (value.getClass().isAssignableFrom(String.class)) {
            cell.setCellValue((String) value);
            cell.setCellStyle(defaultStyle);
        } else if (value.getClass().isAssignableFrom(Long.class)) {
            cell.setCellValue(((Long) value).doubleValue());
            cell.setCellStyle(defaultStyle);
        } else if (value.getClass().isAssignableFrom(Integer.class)) {
            cell.setCellValue(((Integer) value).intValue());
            cell.setCellStyle(defaultStyle);
        } else if (value.getClass().isAssignableFrom(BigDecimal.class)) {
            cell.setCellValue(((BigDecimal) value).doubleValue());
            cell.setCellStyle(amountStyle);
        } else if (value.getClass().isAssignableFrom(LocalDate.class)) {
            cell.setCellValue(Date.from(((LocalDate)value).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
            cell.setCellStyle(dateStyle);
        } else if (value.getClass().isAssignableFrom(LocalDateTime.class)) {
            cell.setCellValue(Date.from(((LocalDateTime)value).atZone(ZoneId.systemDefault()).toInstant()));
            cell.setCellStyle(dateTimeStyle);
        } else {
            throw new IllegalArgumentException("Unexpected cell content: " + value);
        }

        return cell;
    }

    public void alignRight(int colIdx) {
        Cell cell = row.getCell(colIdx);
        if (cell == null) {
            cell = row.createCell(colIdx);
        }
        cell.setCellStyle(rightAlignedStyle);
    }

    public void goToRow(int rowIdx) {
        this.rowIdx = rowIdx;
        this.row = sheet.getRow(rowIdx);
        if (this.row == null) {
            this.row = sheet.createRow(rowIdx);
        }
    }

    public void nextRow() {
        goToRow(++this.rowIdx);
    }

    public int currentRow(){
        return this.rowIdx;
    }

    public void write(OutputStream output) throws IOException {
        workbook.write(output);
        workbook.close();
    }

    public void setUseBorders(boolean useBorders) {
        this.useBorders = useBorders;
    }
}
