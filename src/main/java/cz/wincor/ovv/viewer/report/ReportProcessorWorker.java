package cz.wincor.ovv.viewer.report;

import cz.wincor.ovv.viewer.exception.LargeExportException;
import cz.wincor.ovv.viewer.model.ReportStatus;
import cz.wincor.ovv.viewer.model.entity.Report;
import cz.wincor.ovv.viewer.repository.ReportRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * Worker that process reports from {@link ReportProcessor}.
 *
 * <AUTHOR>
 */
public class ReportProcessorWorker implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(ReportProcessorWorker.class);

    private final Report report;
    private final ReportGenerator generator;
    private final ReportRepository repository;
    private final TransactionTemplate transactionTemplate;


    /**
     * Create new report processing worker for the given {@link Report}.
     *
     * @param report {@link Report} to process. Never {@code null}.
     * @param generator {@link ReportGenerator} which will take care of generating report content.
     * If {@code null}, the report will be marked as {@link ReportStatus#FAILED}.
     * @param repository {@link ReportRepository} for persisting report updates. Never {@code null}.
     */
    public ReportProcessorWorker(Report report, ReportGenerator generator,
            ReportRepository repository, TransactionTemplate transactionTemplate) {
        this.report = report;
        this.generator = generator;
        this.repository = repository;
        this.transactionTemplate = transactionTemplate;
    }

    @Override
    public void run() {
        logger.info("Processing report {}.", report);

        // Update status to IN_PROGRESS in separate transaction
        transactionTemplate.execute((TransactionCallback<Void>) status -> {
            report.setStatus(ReportStatus.IN_PROGRESS);
            repository.save(report);
            return null;
        });

        // Process report and update status
        transactionTemplate.execute((TransactionCallback<Void>) status -> {
            processReport();
            repository.save(report);
            return null;
        });
    }

    private void processReport() {
        try {
            if (generator == null) {
                throw new IllegalStateException("Report generator not defined.");
            }
            if (generator.getSupportedReportType() != report.getType()) {
                throw new IllegalStateException("Invalid report generator usage.");
            }
            byte[] content = generator.generateReport(report);
            report.setData(content);
            report.setStatus(ReportStatus.OK);
            logger.info("Report {} successfully processed.", report);
        } catch (LargeExportException e) {
            logger.error("Exceeded maximum items for report " + report + ".", e);
            report.setStatus(ReportStatus.LARGE_EXPORT);
        } catch (Exception e) {
            logger.error("An error occurred while processing report " + report + ".", e);
            report.setStatus(ReportStatus.FAILED);
        }
    }
}
