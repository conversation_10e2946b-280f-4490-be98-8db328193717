package cz.wincor.ovv.viewer.report.generator.category.processor;

import static cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil.CATEGORY_OTHER;
import static cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil.ISSUER_FANDF;
import static cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil.ISSUER_TVM;
import static cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil.getVolumeCounterByIssuer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cz.wincor.ovv.viewer.model.entity.Voucher;
import cz.wincor.ovv.viewer.report.generator.category.vo.IssuerVolumeCounter;

/**
 * The processor prepares data for the global part of the report (the first sheet)
 *
 * <AUTHOR>
 */
public class GlobalPartDataProcessor implements VoucherDataProcessor {

    private static final Logger logger = LoggerFactory.getLogger(GlobalPartDataProcessor.class);

    private Map<String, List<IssuerVolumeCounter>> categoryStatistics = new HashMap<>();

    private Set<String> issuers = new TreeSet<>(new IssuerNameComparator());

    private Set<String> categories = new TreeSet<>();

    @Override
    public void process(Voucher voucher) {
        String category = voucher.getRelatedTransactionRequest().getCategory();
        if (category == null) {
            category = CATEGORY_OTHER;
        }
        String ovvIssuer = voucher.getRelatedTransactionRequest().getOvvIssuer();
        if (ovvIssuer == null || ovvIssuer.isEmpty()) {
            logger.warn("Skipping voucher  due to misssing issuer code: {}", voucher);
            return;
        }
        issuers.add(ovvIssuer);
        categories.add(category);
        if (!categoryStatistics.containsKey(category)) {
            categoryStatistics.put(category, new ArrayList<>());
        }
        List<IssuerVolumeCounter> issuerVolumeCounters = categoryStatistics.get(category);
        IssuerVolumeCounter issuerVolumeCounter = getVolumeCounterByIssuer(ovvIssuer, issuerVolumeCounters).orElseGet(() -> {
            IssuerVolumeCounter ivc = new IssuerVolumeCounter(ovvIssuer);
            issuerVolumeCounters.add(ivc);
            return ivc;
        });
        issuerVolumeCounter.addSingleItem(voucher.getRelatedTransactionRequest().getAmount());
    }

    public Map<String, List<IssuerVolumeCounter>> getCategoryStatistics() {
        return Collections.unmodifiableMap(categoryStatistics);
    }

    public Set<String> getIssuers() {
        return issuers;
    }

    public Set<String> getCategories() {
        return categories;
    }

    /**
     * We need to have TVM and FandF issuer first (if they exist), other are ordered alphabetically
     */
    static class IssuerNameComparator implements Comparator<String> {

        @Override
        public int compare(String o1, String o2) {
            if (Objects.equals(o1, o2)) {
                return 0;
            }
            if (ISSUER_TVM.equals(o1)) {
                return Integer.MIN_VALUE;
            }
            if (ISSUER_TVM.equals(o2)) {
                return Integer.MAX_VALUE;
            }
            if (ISSUER_FANDF.equals(o1)) {
                return Integer.MIN_VALUE + 1;
            }
            if (ISSUER_FANDF.equals(o2)) {
                return Integer.MAX_VALUE - 1;
            }
            return o1.compareTo(o2);
        }
    }

}
