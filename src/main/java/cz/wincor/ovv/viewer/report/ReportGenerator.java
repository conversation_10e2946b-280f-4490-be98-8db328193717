package cz.wincor.ovv.viewer.report;

import cz.wincor.ovv.viewer.model.ReportType;
import cz.wincor.ovv.viewer.model.entity.Report;

public interface ReportGenerator {

    /**
     * Return type of reports which can be generated by this report generator.
     *
     * @return {@link ReportType}, never {@code null}.
     */
    ReportType getSupportedReportType();

    /**
     * Generate report content for a specific submitted {@link Report}.
     *
     * @param report Report whose content should be generated. Never {@code null}.
     * @return Report content as a byte array.
     */
    byte[] generateReport(Report report);

}
