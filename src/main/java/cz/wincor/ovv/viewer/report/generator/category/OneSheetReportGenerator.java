package cz.wincor.ovv.viewer.report.generator.category;

import cz.wincor.ovv.viewer.dto.ReportParams;
import cz.wincor.ovv.viewer.report.XlsReportWorkbook;
import cz.wincor.ovv.viewer.report.generator.category.processor.CommonVoucherDataProcessor;

public interface OneSheetReportGenerator {

    void writeOneSheetReport(ReportParams params, XlsReportWorkbook workbook, CommonVoucherDataProcessor commonVoucherDataProcessor);
}
