package cz.wincor.ovv.viewer.report.generator.category;

import static cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil.GENERATED_TIME_FORMAT;
import static cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil.HUNDRED;
import static cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil.PARAM_DATE_FORMAT;
import static cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil.getVolumeCounterByIssuer;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cz.wincor.ovv.viewer.dto.ReportParams;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.report.XlsReportWorkbook;
import cz.wincor.ovv.viewer.report.generator.category.processor.CommonVoucherDataProcessor;
import cz.wincor.ovv.viewer.report.generator.category.vo.IssuerVolumeCounter;
import cz.wincor.ovv.viewer.repository.StoreRepository;

/**
 * The class responsible for generating the first sheet in the Category report
 * <AUTHOR>
 */
@Component
public class GlobalPartReportGenerator implements OneSheetReportGenerator {

    @Autowired
    private StoreRepository storeRepository;

    @Override
    public void writeOneSheetReport(ReportParams params, XlsReportWorkbook workbook, CommonVoucherDataProcessor commonVoucherDataProcessor) {
        writeTableHeaders(params, workbook, commonVoucherDataProcessor);
        writeTableData(workbook, commonVoucherDataProcessor);
    }

    private void writeTableData(XlsReportWorkbook workbook, CommonVoucherDataProcessor commonVoucherDataProcessor) {
        Set<String> uniqueCategories = commonVoucherDataProcessor.getGlobalReportUniqueCategories();
        Map<String, List<IssuerVolumeCounter>> statistics = commonVoucherDataProcessor.getGlobalCategoryStatistics();
        int column = 2;
        Set<String> issuers = commonVoucherDataProcessor.getGlobalReportUniqueIssuers();
        for (String uniqueCategory : uniqueCategories) {
            List<IssuerVolumeCounter> issuerVolumeCounters = statistics.get(uniqueCategory);
            workbook.writeValue(column, 0, 2, uniqueCategory);
            column = column + 2;
            IssuerVolumeCounter totalIssuerCounter = new IssuerVolumeCounter();
            for (String issuer : issuers) {
                IssuerVolumeCounter counter = getVolumeCounterByIssuer(issuer, issuerVolumeCounters).orElse(new IssuerVolumeCounter());
                workbook.writeValue(column++, new BigDecimal(counter.getAmount()).divide(HUNDRED));
                workbook.writeValue(column++, counter.getCount());
                totalIssuerCounter.add(counter.getCount(), counter.getAmount());
            }
            workbook.writeValue(column++, new BigDecimal(totalIssuerCounter.getAmount()).divide(HUNDRED));
            workbook.writeValue(column++, totalIssuerCounter.getCount());
            workbook.nextRow();
            column = 2;
        }
        // Append total for each issuer
        workbook.applyBoldStyle(workbook.writeValue(column, 0, 2, "Total"));
        column = column + 2;
        IssuerVolumeCounter globalTotalAll = new IssuerVolumeCounter();
        for (String issuer : issuers) {
            IssuerVolumeCounter counter = commonVoucherDataProcessor.getGlobalTotalVolumeCounterByIssuer(issuer);
            workbook.applyBoldStyle(workbook.writeValue(column++, new BigDecimal(counter.getAmount()).divide(HUNDRED)));
            workbook.applyBoldStyle(workbook.writeValue(column++, counter.getCount()));
            globalTotalAll.add(counter.getCount(), counter.getAmount());
        }
        workbook.applyBoldStyle(workbook.writeValue(column++, new BigDecimal(globalTotalAll.getAmount()).divide(HUNDRED)));
        workbook.applyBoldStyle(workbook.writeValue(column++, globalTotalAll.getCount()));

    }

    private void writeTableHeaders(ReportParams params, XlsReportWorkbook workbook, CommonVoucherDataProcessor commonVoucherDataProcessor) {
        workbook.goToRow(1);
        Set<String> uniqueIssuers = commonVoucherDataProcessor.getGlobalReportUniqueIssuers();
        Store store = null;
        if (params.getStoreId() != null) {
            store = findStore(params.getStoreId());
        }
        String info = "%s - %s / %s - %s (Generated: %s)";
        info = String.format(info,
                store.getSiteCode(),
                store.getName(),
                PARAM_DATE_FORMAT.format(params.getFromDate()),
                PARAM_DATE_FORMAT.format(params.getToDate()),
                GENERATED_TIME_FORMAT.format(LocalDateTime.now())
        );
        workbook.writeValue(0, 0, 6 + uniqueIssuers.size() * 2, info);
        workbook.nextRow();

        workbook.applyHeaderStyle(workbook.writeValue(0, 2, 2, "Store"));
        workbook.applyHeaderStyle(workbook.writeValue(2, 2, 1, "Kind of voucher"));
        workbook.applyHeaderStyle(workbook.writeValue(3, ""));

        workbook.applyHeaderStyle(workbook.writeValue(4, 0, uniqueIssuers.size() * 2 + 2, "Amount and Number of redeemed vouchers"));
        workbook.nextRow();
        workbook.applyHeaderStyle(workbook.writeValue(3, "Issuer"));
        int column = 4;
        for (String uniqueIssuer : uniqueIssuers) {
            workbook.applyHeaderStyle(workbook.writeValue(column, 0, 2, uniqueIssuer));
            column = column + 2;
        }
        workbook.applyHeaderStyle(workbook.writeValue(column, 1, 1, "Total amount"));
        workbook.applyHeaderStyle(workbook.writeValue(++column, 1, 1, "Total number"));
        workbook.nextRow();

        workbook.applyHeaderStyle(workbook.writeValue(3, ""));
        column = 4;
        for (String uniqueIssuer : uniqueIssuers) {
            workbook.applyHeaderStyle(workbook.writeValue(column++, "Amount"));
            workbook.applyHeaderStyle(workbook.writeValue(column++, "Number"));
        }
        workbook.nextRow();
        Set<String> uniqueCategories = commonVoucherDataProcessor.getGlobalReportUniqueCategories();
        if (store != null) {
            workbook.writeValue(0, uniqueCategories.size(), 1, store.getSiteCode());
            workbook.writeValue(1, uniqueCategories.size(), 1, store.getName());
        }
    }

    private Store findStore(long id) {
        return storeRepository.findById(id).orElse(null);
    }
}
