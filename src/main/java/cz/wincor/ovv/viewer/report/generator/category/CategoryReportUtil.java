package cz.wincor.ovv.viewer.report.generator.category;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

import cz.wincor.ovv.viewer.report.generator.category.vo.CategoryAndCheckoutStatistic;
import cz.wincor.ovv.viewer.report.generator.category.vo.IssuerVolumeCounter;
import cz.wincor.ovv.viewer.utils.NaturalOrderComparators;

/**
 * Helper util class for Category report
 * <AUTHOR>
 */
public class CategoryReportUtil {

    public static final DateTimeFormatter GENERATED_TIME_FORMAT = DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm:ss");
    public static final DateTimeFormatter PARAM_DATE_FORMAT = DateTimeFormatter.ofPattern("dd.MM.yyyy");
    public static final BigDecimal HUNDRED = new BigDecimal(100);
    public static final String CATEGORY_OTHER = "OTHER";
    public static final String ISSUER_TVM = "TVM";
    public static final String ISSUER_FANDF = "FandF";

    /**
     * Find {@link CategoryAndCheckoutStatistic} by category and checkout number
     * @param checkout
     * @param category
     * @param statisticsList
     * @return found {@link CategoryAndCheckoutStatistic} or merged new {@link CategoryAndCheckoutStatistic} if found more than one
     */
    public static Optional<CategoryAndCheckoutStatistic> findByCheckoutAndCategory(String checkout, String category, List<CategoryAndCheckoutStatistic> statisticsList) {
        List<CategoryAndCheckoutStatistic> checkoutStatistics = statisticsList.stream()
                .filter(stat -> Objects.equals(checkout, stat.getCheckout()) && Objects.equals(category, stat.getCategory()))
                .collect(Collectors.toList());
        if (checkoutStatistics.isEmpty()) {
            return Optional.empty();
        }
        if (checkoutStatistics.size() == 1) {
            return Optional.of(checkoutStatistics.get(0));
        }
        CategoryAndCheckoutStatistic newCheckoutStatistic = new CategoryAndCheckoutStatistic(category, checkout);
        for (CategoryAndCheckoutStatistic statistic : checkoutStatistics) {
            newCheckoutStatistic.getVolumeCounters().addAll(statistic.getVolumeCounters());
        }
        return Optional.of(newCheckoutStatistic);
    }

    /**
     * Find {@link CategoryAndCheckoutStatistic} by category only
     * @param category
     * @param statisticsList
     * @return found {@link CategoryAndCheckoutStatistic} or merged new {@link CategoryAndCheckoutStatistic} if found more than one
     */
    public static Optional<CategoryAndCheckoutStatistic> findByCategory(String category, List<CategoryAndCheckoutStatistic> statisticsList) {
        List<CategoryAndCheckoutStatistic> checkoutStatistics = statisticsList.stream()
                .filter(stat -> Objects.equals(category, stat.getCategory()))
                .collect(Collectors.toList());
        if (checkoutStatistics.isEmpty()) {
            return Optional.empty();
        }
        if (checkoutStatistics.size() == 1) {
            return Optional.of(checkoutStatistics.get(0));
        }
        CategoryAndCheckoutStatistic newCheckoutStatistic = new CategoryAndCheckoutStatistic(category, null);
        for (CategoryAndCheckoutStatistic statistic : checkoutStatistics) {
            newCheckoutStatistic.getVolumeCounters().addAll(statistic.getVolumeCounters());
        }
        return Optional.of(newCheckoutStatistic);
    }

    /**
     * Find all {@link IssuerVolumeCounter}s by checkout number from list of {@link CategoryAndCheckoutStatistic}
     * @param checkout
     * @param statisticsList
     * @return found list of {@link IssuerVolumeCounter}, or empty list, never null
     */
    public static List<IssuerVolumeCounter> findAllCountersByCheckout(String checkout, List<CategoryAndCheckoutStatistic> statisticsList) {
        List<IssuerVolumeCounter> issuerVolumeCounters = new ArrayList<>();
        statisticsList.stream()
                .filter(stat -> Objects.equals(checkout, stat.getCheckout()))
                .map(CategoryAndCheckoutStatistic::getVolumeCounters)
                .forEach(issuerVolumeCounters::addAll);
        return issuerVolumeCounters;
    }

    /**
     * Get {@link IssuerVolumeCounter} by issuer name from list of {@link IssuerVolumeCounter}
     * @param ovvIssuer
     * @param volumeCounters
     * @return found {@link IssuerVolumeCounter} or merged IssuerVolumeCounter if found more than one, never null
     */
    public static Optional<IssuerVolumeCounter> getVolumeCounterByIssuer(String ovvIssuer, List<IssuerVolumeCounter> volumeCounters) {
        List<IssuerVolumeCounter> counters = volumeCounters.stream()
                .filter(it -> ovvIssuer.equals(it.getIssuer()))
                .collect(Collectors.toList());
        if (counters.isEmpty()) {
            return Optional.empty();
        }
        if (counters.size() == 1) {
            return Optional.of(counters.get(0));
        }
        IssuerVolumeCounter newCounter = new IssuerVolumeCounter(ovvIssuer);
        for (IssuerVolumeCounter counter : counters) {
            newCounter.add(counter.getCount(), counter.getAmount());
        }
        return Optional.of(newCounter);
    }

    /**
     * Get single {@link IssuerVolumeCounter} from list of {@link CategoryAndCheckoutStatistic} by issuer name
     * All found {@link IssuerVolumeCounter} are merged into single {@link IssuerVolumeCounter}
     * @param ovvIssuer
     * @param statistics
     * @return merged all found {@link IssuerVolumeCounter}
     */
    public static IssuerVolumeCounter getVolumeCounterByIssuerFromStatistics(String ovvIssuer, List<CategoryAndCheckoutStatistic> statistics) {
        IssuerVolumeCounter newCounter = new IssuerVolumeCounter(ovvIssuer);
        for (CategoryAndCheckoutStatistic statistic : statistics) {
            IssuerVolumeCounter counterByIssuer = getVolumeCounterByIssuer(ovvIssuer, statistic.getVolumeCounters()).orElse(new IssuerVolumeCounter());
            newCounter.add(counterByIssuer.getCount(), counterByIssuer.getAmount());
        }
        return newCounter;
    }

    /**
     * Get {@link IssuerVolumeCounter} from list of {@link IssuerVolumeCounter} except {@link IssuerVolumeCounter} related to defined exceptIssuer
     * @param volumeCounters
     * @param exceptIssuer list of issuer names which should be skipped
     * @return found {@link IssuerVolumeCounter}, or merged {@link IssuerVolumeCounter} if found more than once, never null
     */
    public static Optional<IssuerVolumeCounter> getVolumeCounterExceptIssuers(List<IssuerVolumeCounter> volumeCounters, String... exceptIssuer) {
        List<String> exceptIssuerList = Arrays.asList(exceptIssuer);
        List<IssuerVolumeCounter> counters = volumeCounters.stream()
                .filter(it -> !exceptIssuerList.contains(it.getIssuer()))
                .collect(Collectors.toList());
        if (counters.isEmpty()) {
            return Optional.empty();
        }
        if (counters.size() == 1) {
            return Optional.of(counters.get(0));
        }
        IssuerVolumeCounter newCounter = new IssuerVolumeCounter();
        for (IssuerVolumeCounter counter : counters) {
            newCounter.add(counter.getCount(), counter.getAmount());
        }
        return Optional.of(newCounter);
    }

    /**
     * Get unique checkout numbers (returned as Set of String) from list of {@link CategoryAndCheckoutStatistic}
     * @param value
     * @return ordered {@link Set} of checkout numbers, never null, empty {@link Set} otherwise
     */
    public static Set<String> getUniqueCheckouts(List<CategoryAndCheckoutStatistic> value) {
        return value.stream()
                .map(CategoryAndCheckoutStatistic::getCheckout)
                .collect(Collectors.toCollection(()->new TreeSet<>(NaturalOrderComparators.createNaturalOrderRegexComparator())));
    }

    /**
     * Merge all input {@link IssuerVolumeCounter}s into one {@link IssuerVolumeCounter}
     * @param counters
     * @return
     */
    public static IssuerVolumeCounter sumAllCounters(IssuerVolumeCounter... counters) {
        IssuerVolumeCounter total = new IssuerVolumeCounter();
        for (IssuerVolumeCounter counter : counters) {
            total.add(counter.getCount(), counter.getAmount());
        }
        return total;
    }
}
