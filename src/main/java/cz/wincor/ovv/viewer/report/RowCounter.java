package cz.wincor.ovv.viewer.report;

import cz.wincor.ovv.viewer.exception.LargeExportException;

/**
 * Row counter class checking if number of records in the report is below the allowed maximum.
 */
public class RowCounter {
    private static final int MAX_RECORDS_COUNT = 1000000;

    private int index = 0;

    public void increment() {
        if (++index > MAX_RECORDS_COUNT) {
            throw new LargeExportException("Number of records reached the limit (" + MAX_RECORDS_COUNT + ").");
        }
    }

    public int getIndex() {
        return this.index;
    }
}
