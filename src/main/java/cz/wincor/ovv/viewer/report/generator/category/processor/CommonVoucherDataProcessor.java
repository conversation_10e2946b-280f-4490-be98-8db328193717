package cz.wincor.ovv.viewer.report.generator.category.processor;

import static cz.wincor.ovv.viewer.report.generator.category.CategoryReportUtil.getVolumeCounterByIssuer;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import cz.wincor.ovv.viewer.model.entity.Voucher;
import cz.wincor.ovv.viewer.report.generator.category.vo.CategoryAndCheckoutStatistic;
import cz.wincor.ovv.viewer.report.generator.category.vo.IssuerVolumeCounter;

/**
 * The processor prepares data for all sheets
 * (It actually delegates to some other processors)
 * <AUTHOR>
 */
public class CommonVoucherDataProcessor implements VoucherDataProcessor {

    private GlobalPartDataProcessor globalPartDataProcessor = new GlobalPartDataProcessor();
    private DailyPartDataProcessor dailyPartDataProcessor = new DailyPartDataProcessor();

    public void process(Voucher voucher) {
        globalPartDataProcessor.process(voucher);
        dailyPartDataProcessor.process(voucher);

    }

    public Map<String, List<IssuerVolumeCounter>> getGlobalCategoryStatistics() {
        return globalPartDataProcessor.getCategoryStatistics();
    }

    public Map<LocalDate, List<CategoryAndCheckoutStatistic>> getDailyCategoryStatistics() {
        return dailyPartDataProcessor.getStatistics();
    }

    public Set<String> getGlobalReportUniqueIssuers() {
        return globalPartDataProcessor.getIssuers();
    }

    public Set<String> getGlobalReportUniqueCategories() {
        return globalPartDataProcessor.getCategories();
    }

    public IssuerVolumeCounter getGlobalTotalVolumeCounterByIssuer(String ovvIssuer) {
        Map<String, List<IssuerVolumeCounter>> statistics = globalPartDataProcessor.getCategoryStatistics();
        IssuerVolumeCounter total = new IssuerVolumeCounter(ovvIssuer);
        for (List<IssuerVolumeCounter> counters : statistics.values()) {
            Optional<IssuerVolumeCounter> volumeCounterByIssuer = getVolumeCounterByIssuer(ovvIssuer, counters);
            if (volumeCounterByIssuer.isPresent()) {
                total.add(volumeCounterByIssuer.get().getCount(), volumeCounterByIssuer.get().getAmount());
            }
        }
        return total;
    }

    public Set<String> getDailyReportUniqueTvmCategories() {
        return dailyPartDataProcessor.getTvmCategories();
    }

    public Set<String> getDailyReportUniqueExternalCategories() {
        return dailyPartDataProcessor.getExternalCategories();
    }


}
