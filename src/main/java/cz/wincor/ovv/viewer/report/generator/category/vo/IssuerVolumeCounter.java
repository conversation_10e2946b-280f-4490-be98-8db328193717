package cz.wincor.ovv.viewer.report.generator.category.vo;

public class IssuerVolumeCounter {

    private String issuer;
    private long amount;
    private int count;

    public IssuerVolumeCounter(String issuer) {
        this.issuer = issuer;
    }

    public IssuerVolumeCounter() {
    }

    public void addSingleItem(long amount) {
        this.amount = this.amount + amount;
        count++;
    }

    public long getAmount() {
        return amount;
    }

    public int getCount() {
        return count;
    }

    public String getIssuer() {
        return issuer;
    }

    public void add(int count, long amount) {
        this.count = this.count + count;
        this.amount = this.amount + amount;
    }

    public boolean isEmpty() {
        return count == 0;
    }
}
