package cz.wincor.ovv.viewer.dto;

import java.util.List;

/**
 * Page of DTO objects. Wraps a sublist of a all potential result elements.
 *
 * <AUTHOR>
 *
 * @param <T> Type of DTO.
 */
public class DtoPage<T> {

    /**
     * List of DTOs.
     */
    private List<T> data;
    /**
     * Total number of elements.
     */
    private long total;


    public List<T> getData() {
        return data;
    }
    public void setData(List<T> data) {
        this.data = data;
    }

    public long getTotal() {
        return total;
    }
    public void setTotal(long total) {
        this.total = total;
    }
}
