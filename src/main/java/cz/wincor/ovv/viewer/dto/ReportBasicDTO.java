package cz.wincor.ovv.viewer.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;

import cz.wincor.ovv.viewer.model.ReportStatus;
import cz.wincor.ovv.viewer.model.ReportType;

/**
 * DTO with basic info of a Report entity.
 *
 * <AUTHOR>
 */
public class ReportBasicDTO {

    private long id;
    private ReportType type;
    private ReportStatus status;
    private BasicEntityDTO store;
    private LocalDateTime createdDate;
    private LocalDateTime finishedDate;
    private LocalDate dataFrom;
    private LocalDate dataTo;


    //~ Plain getters & setters

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public ReportType getType() {
        return type;
    }

    public void setType(ReportType type) {
        this.type = type;
    }

    public ReportStatus getStatus() {
        return status;
    }

    public void setStatus(ReportStatus status) {
        this.status = status;
    }

    public BasicEntityDTO getStore() {
        return store;
    }

    public void setStore(BasicEntityDTO store) {
        this.store = store;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDateTime getFinishedDate() {
        return finishedDate;
    }

    public void setFinishedDate(LocalDateTime finishedDate) {
        this.finishedDate = finishedDate;
    }

    public LocalDate getDataFrom() {
        return dataFrom;
    }

    public void setDataFrom(LocalDate dataFrom) {
        this.dataFrom = dataFrom;
    }

    public LocalDate getDataTo() {
        return dataTo;
    }

    public void setDataTo(LocalDate dataTo) {
        this.dataTo = dataTo;
    }
}
