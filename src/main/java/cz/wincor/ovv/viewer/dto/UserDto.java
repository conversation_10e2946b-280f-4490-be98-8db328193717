package cz.wincor.ovv.viewer.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.model.UserRole;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.model.entity.User;

/**
 * DTO for {@link User} entity.
 *
 * <AUTHOR>
 */
public class UserDto {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;
    private String username;
    private String firstName;
    private String lastName;
    private String email;
    private UserRole role;
    private boolean active;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String password;
    private List<Store> stores;
    private CountryEnum countryCode;


    //~ Getters & setters

    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }
    public void setUsername(String username) {
        this.username = username;
    }

    public String getFirstName() {
        return firstName;
    }
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }
    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isActive() {
        return active;
    }
    public void setActive(boolean active) {
        this.active = active;
    }

    public String getPassword() {
        return password;
    }
    public void setPassword(String password) {
        this.password = password;
    }

    public UserRole getRole() {
        return role;
    }
    public void setRole(UserRole role) {
        this.role = role;
    }
    public List<Store> getStores() {
        return stores;
    }
    public void setStores(List<Store> stores) {
        this.stores = stores;
    }

    public CountryEnum getCountryCode() {
        return countryCode;
    }
    public void setCountryCode(CountryEnum countryCode) {
        this.countryCode = countryCode;
    }
    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("UserDto [id=");
        builder.append(id);
        builder.append(", username=");
        builder.append(username);
        builder.append(", firstName=");
        builder.append(firstName);
        builder.append(", lastName=");
        builder.append(lastName);
        builder.append(", email=");
        builder.append(email);
        builder.append(", role=");
        builder.append(role);
        builder.append(", active=");
        builder.append(active);
        builder.append(", stores=");
        builder.append(stores);
        builder.append(", country=");
        builder.append(countryCode);
        builder.append("]");
        return builder.toString();
    }



}
