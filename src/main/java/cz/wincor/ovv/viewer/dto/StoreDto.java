package cz.wincor.ovv.viewer.dto;

import cz.wincor.ovv.viewer.enums.CountryEnum;

public class StoreDto {

    private Long id;

    private String costCentre;

    private CountryEnum countryCode;

    private String name;

    private String siteCode;

    private String partnerId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCostCentre() {
        return costCentre;
    }

    public void setCostCentre(String costCentre) {
        this.costCentre = costCentre;
    }

    public CountryEnum getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(CountryEnum countryCode) {
        this.countryCode = countryCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }
}
