package cz.wincor.ovv.viewer.dto;

import java.io.Serializable;

public class BasicEntityDTO implements Serializable {

    private static final long serialVersionUID = -2494979879179989961L;

    private long id;
    private String name;

    public BasicEntityDTO() {
    }

    public BasicEntityDTO(long id, String name) {
        this.id = id;
        this.name = name;
    }

    //~ Getters & setters

    public long getId() {
        return id;
    }
    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
}
