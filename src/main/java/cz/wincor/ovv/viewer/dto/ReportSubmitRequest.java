package cz.wincor.ovv.viewer.dto;

import cz.wincor.ovv.viewer.model.ReportType;

/**
 * Report submit request defining type of report and input parameters.
 *
 * <AUTHOR>
 */
public class ReportSubmitRequest {

    private ReportType type;
    private ReportParams params;


    //~ Plain getters & setters

    public ReportType getType() {
        return type;
    }
    public void setType(ReportType type) {
        this.type = type;
    }

    public ReportParams getParams() {
        return params;
    }
    public void setParams(ReportParams params) {
        this.params = params;
    }
}
