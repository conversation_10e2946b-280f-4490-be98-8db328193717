package cz.wincor.ovv.viewer.dto;

import cz.wincor.ovv.viewer.model.entity.Store;

public class VoucherDto {
    private String transactionType;
    private String voucherNumber;
    private Store store;
    private Long amount;
    private String redemptionDate;
    private String device;
    private int stan;

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getVoucherNumber() {
        return voucherNumber;
    }

    public void setVoucherNumber(String voucherNumber) {
        this.voucherNumber = voucherNumber;
    }

    public Store getStore() {
        return store;
    }

    public void setStore(Store store) {
        this.store = store;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getRedemptionDate() {
        return redemptionDate;
    }

    public void setRedemptionDate(String redemptionDate) {
        this.redemptionDate = redemptionDate;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public int getStan() {
        return stan;
    }

    public void setStan(int stan) {
        this.stan = stan;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("VoucherDto [transactionType=");
        builder.append(transactionType);
        builder.append(", voucherNumber=");
        builder.append(voucherNumber);
        builder.append(", store=");
        builder.append(store);
        builder.append(", amount=");
        builder.append(amount);
        builder.append(", redemptionDate=");
        builder.append(redemptionDate);
        builder.append(", device=");
        builder.append(device);
        builder.append(", stan=");
        builder.append(stan);
        builder.append("]");
        return builder.toString();
    }


}
