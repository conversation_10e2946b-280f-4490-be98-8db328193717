package cz.wincor.ovv.viewer.dto;

import java.time.LocalDate;

import org.apache.commons.lang3.builder.ToStringBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * Common input parameters for reports.
 *
 * <AUTHOR>
 */
@JsonInclude(Include.NON_NULL)
public class ReportParams {

    private LocalDate fromDate;
    private LocalDate toDate;
    private Long storeId;


    //~ Plain getters & setters

    public LocalDate getFromDate() {
        return fromDate;
    }

    public void setFromDate(LocalDate fromDate) {
        this.fromDate = fromDate;
    }

    public LocalDate getToDate() {
        return toDate;
    }

    public void setToDate(LocalDate toDate) {
        this.toDate = toDate;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("fromDate", fromDate)
                .append("toDate", toDate)
                .append("storeId", storeId)
                .toString();
    }
}
