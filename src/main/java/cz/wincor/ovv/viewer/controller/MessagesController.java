package cz.wincor.ovv.viewer.controller;

import java.util.Locale;
import java.util.Properties;

import org.apache.commons.lang3.LocaleUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import cz.wincor.base.spring.context.support.SerializableResourceBundleMessageSource;

/**
 * Controller providing access to localization messages.
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/messages")
public class MessagesController {

    @Autowired
    private SerializableResourceBundleMessageSource messageBundle;


    @GetMapping
    @ResponseBody
    public Properties list(@RequestParam(name = "lang", required = false) String lang) {
        Locale locale;
        if (StringUtils.isNotBlank(lang)) {
            locale = LocaleUtils.toLocale(lang);
        } else {
            locale = LocaleContextHolder.getLocale();
        }
        return messageBundle.getAllProperties(locale);
    }
}
