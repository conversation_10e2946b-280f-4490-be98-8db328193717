package cz.wincor.ovv.viewer.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import cz.wincor.ovv.viewer.bootstrap.ConfigParams;

/**
 * Controller providing access to application configuration parameters.
 *
 * <AUTHOR>
 * @see ConfigParams
 */
@Controller
@RequestMapping("/configuration")
public class ConfigParamsController {

    @Autowired
    private ConfigParams configParams;


    @RequestMapping(method = RequestMethod.GET)
    @ResponseBody
    public ConfigParams getConfigParams() {
        return configParams;
    }

}
