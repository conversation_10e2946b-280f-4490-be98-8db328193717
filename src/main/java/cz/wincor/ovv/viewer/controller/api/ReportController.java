package cz.wincor.ovv.viewer.controller.api;

import cz.wincor.ovv.viewer.controller.api.vo.DrawRequest;
import cz.wincor.ovv.viewer.controller.api.vo.DrawResult;
import cz.wincor.ovv.viewer.dto.DtoPage;
import cz.wincor.ovv.viewer.dto.ReportBasicDTO;
import cz.wincor.ovv.viewer.dto.ReportDTO;
import cz.wincor.ovv.viewer.dto.ReportSubmitRequest;
import cz.wincor.ovv.viewer.exception.NotFoundException;
import cz.wincor.ovv.viewer.exception.ValidationException;
import cz.wincor.ovv.viewer.model.search.ReportSearchCriteria;
import cz.wincor.ovv.viewer.service.ReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


/**
 * Controller for report management.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/reports")
public class ReportController {

    @Autowired
    private ReportService reportService;
    /**
     * Search reports with paging.
     */
    @RequestMapping(method = RequestMethod.GET)
    public DrawResult<ReportBasicDTO> getReports(ReportSearchCriteria searchCriteria, DrawRequest drawRequest) {
        drawRequest.mapToSearchCriteria(searchCriteria);
        DtoPage<ReportBasicDTO> reports = reportService.getReports(searchCriteria);
        return new DrawResult<>(drawRequest, reports);
    }

    @RequestMapping(value = "/{reportId}/data", method = RequestMethod.GET)
    public HttpEntity<byte[]> getReportContent(@PathVariable("reportId") long reportId) throws NotFoundException {
        ReportDTO report = reportService.getReportById(reportId);

        HttpHeaders header = new HttpHeaders();
        header.setContentType(new MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
        header.setContentLength(report.getData().length);
        header.setContentDispositionFormData("attachment", "report-" + reportId + ".xlsx");

        return new HttpEntity<>(report.getData(), header);
    }

    @RequestMapping(method = RequestMethod.POST)
    public ReportDTO createReport(@RequestBody ReportSubmitRequest request) throws ValidationException {
        return reportService.submitReport(request);
    }
}
