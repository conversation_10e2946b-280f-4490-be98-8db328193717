package cz.wincor.ovv.viewer.controller.api;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.enums.StoreSearchScope;
import cz.wincor.ovv.viewer.model.entity.CashRegister;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.service.StoreService;

@RestController
@RequestMapping("/api/cashRegisters")
public class CashRegisterController {

    @Autowired
    private StoreService storeService;

    @GetMapping(value = "/find")
    public List<CashRegister> findCashRegister(@RequestParam("siteCode") String siteCode,
            @RequestParam("number") String number) {
        return storeService.findStoreCashRegisters(siteCode, number);
    }

}
