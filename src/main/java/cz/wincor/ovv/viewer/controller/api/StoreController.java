package cz.wincor.ovv.viewer.controller.api;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.enums.StoreSearchScope;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.service.StoreService;

@RestController
@RequestMapping("/api/stores")
public class StoreController {

    @Autowired
    private StoreService storeService;

    @GetMapping(value = "/find")
    public List<Store> find(@RequestParam("text") String text, @RequestParam(name = "countryCode", required = false) CountryEnum country, @RequestParam(name = "searchScope", required = false) String searchScope) {
        StoreSearchScope searchScopeEnum = getSearchScope(searchScope);
        return storeService.findStore(text, country, searchScopeEnum);
    }

    private StoreSearchScope getSearchScope(String searchScope) {
        StoreSearchScope searchScopeEnum = null;
        if (StringUtils.isBlank(searchScope)) {
            searchScopeEnum = StoreSearchScope.USER;
        }
        else {
            try {
            searchScopeEnum = StoreSearchScope.valueOf(searchScope);
            } catch(IllegalArgumentException e) {
                searchScopeEnum = StoreSearchScope.USER;
            }
        }
        return searchScopeEnum;
    }

}
