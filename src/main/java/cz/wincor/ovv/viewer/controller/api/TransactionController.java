package cz.wincor.ovv.viewer.controller.api;

import cz.wincor.ovv.viewer.controller.api.vo.DrawRequest;
import cz.wincor.ovv.viewer.controller.api.vo.DrawResult;
import cz.wincor.ovv.viewer.exception.LargeExportException;
import cz.wincor.ovv.viewer.exception.NotFoundException;
import cz.wincor.ovv.viewer.model.entity.Transaction;
import cz.wincor.ovv.viewer.model.entity.User;
import cz.wincor.ovv.viewer.model.search.TransactionSearchCriteria;
import cz.wincor.ovv.viewer.security.SecurityUtils;
import cz.wincor.ovv.viewer.service.ViewerService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDateTime;

@RestController
@RequestMapping("/api/transactions")
public class TransactionController {
    private static final String XLSX_CONTENT_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    private static final String FILENAME_HEADER_KEY = "Content-disposition";
    private static final String FILENAME_HEADER_VALUE = "attachment; filename=transactions_%tF.xlsx";

    @Autowired
    private ViewerService viewerService;

    @GetMapping
    public DrawResult<Transaction> getTransactions(TransactionSearchCriteria searchCriteria, DrawRequest drawRequest) {
        drawRequest.mapToSearchCriteria(searchCriteria);
        Page<Transaction> page = viewerService.getTransactions(searchCriteria);
        return new DrawResult<>(drawRequest, page);
    }

    @GetMapping(path = "/{transactionId}")
    public Transaction getTransaction(@PathVariable("transactionId") long transactionId) throws NotFoundException {
        return viewerService.getTransaction(transactionId);
    }

    @GetMapping("/xls")
    public ResponseEntity<Void> getTransactionXls(TransactionSearchCriteria searchCriteria, DrawRequest drawRequest,
                                                  HttpServletRequest request, HttpServletResponse response) {

        User loggedUser = SecurityUtils.getLoggedUser();
        if (!loggedUser.getRole().name().equals("ADMIN")
                && !searchCriteria.getCountryCode().name().equals(loggedUser.getCountryCode().name())) {
            return new ResponseEntity<Void>(HttpStatus.BAD_REQUEST);
        }

        drawRequest.mapToSearchCriteria(searchCriteria);

        File tmpFile = null;
        try {
            tmpFile = File.createTempFile("transactions", ".xlsx");

            viewerService.getTransactionXls(searchCriteria, tmpFile);
            response.setContentType(XLSX_CONTENT_TYPE);
            response.setHeader(FILENAME_HEADER_KEY, String.format(FILENAME_HEADER_VALUE, LocalDateTime.now()));

            Files.copy(tmpFile.toPath(), response.getOutputStream());
        } catch (LargeExportException e) {
            try {
                response.sendRedirect(request.getContextPath() + "/export-failed.html");
            } catch (IOException e1) {
                throw new RuntimeException(e1);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (tmpFile != null) {
                tmpFile.delete();
            }
        }
        return new ResponseEntity<Void>(HttpStatus.OK);
    }
}
