package cz.wincor.ovv.viewer.controller.api;

import org.springframework.validation.Errors;
import org.springframework.validation.ValidationUtils;
import org.springframework.validation.Validator;

/**
 * Created by jonas.ruzicka on 22.3.2018.
 */
public class PasswordChangeFormValidator implements Validator {
    @Override
    public boolean supports(Class<?> clazz) {
        return PasswordChangeForm.class.equals(clazz);
    }

    @Override
    public void validate(Object target, Errors errors) {
        ValidationUtils.rejectIfEmptyOrWhitespace(errors, "oldPassword", "oldPassword.empty");
        ValidationUtils.rejectIfEmptyOrWhitespace(errors, "newPassword", "newPassword.empty");
        ValidationUtils.rejectIfEmptyOrWhitespace(errors, "newPasswordConfirm", "newPasswordConfirm.empty");
        PasswordChangeForm form = (PasswordChangeForm) target;
        if (form.getNewPassword()!=null && form.getNewPasswordConfirm()!= null &&
                !form.getNewPassword().equals(form.getNewPasswordConfirm())) {
            errors.rejectValue("newPassword", "newPassword.noMatch");
        }
        if (form.getOldPassword()!=null && form.getNewPasswordConfirm()!= null &&
                form.getOldPassword().equals(form.getNewPasswordConfirm())) {
            errors.rejectValue("newPassword", "newPassword.sameWithOld");
        }
    }
}

