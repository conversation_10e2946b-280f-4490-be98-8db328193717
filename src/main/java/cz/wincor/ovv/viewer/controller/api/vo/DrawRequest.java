package cz.wincor.ovv.viewer.controller.api.vo;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;

import cz.wincor.ovv.viewer.model.search.BaseSearchCriteria;

/**
 * Value object representing a single request of a single DataTables draw.
 *
 * <AUTHOR>
 */
public class DrawRequest {

    private int draw;
    private int length;
    private int start;
    private String orderKey;
    private String orderDir;


    /**
     * Map data of this draw request to the {@link BaseSearchCriteria} which will be sent
     * to the back-end for search processing.
     * @param searchCriteria Search criteria to which the request should be mapped. Never {@code null}.
     */
    public void mapToSearchCriteria(BaseSearchCriteria searchCriteria) {
        if (length > 0) {
            // Paging requested
            searchCriteria.setPage(start / length);
            searchCriteria.setSize(length);
        }
        if (orderKey != null && StringUtils.isNotBlank(orderKey)) {
            searchCriteria.addSorting(orderKey);
        }
        if (orderDir != null && StringUtils.isNotBlank(orderKey)) {
            searchCriteria.setDirection(Sort.Direction.valueOf(orderDir.toUpperCase()));
        }
    }

    //~ Plain getters & setters

    public int getDraw() {
        return draw;
    }
    public void setDraw(int draw) {
        this.draw = draw;
    }

    public int getLength() {
        return length;
    }
    public void setLength(int length) {
        this.length = length;
    }

    public int getStart() {
        return start;
    }
    public void setStart(int start) {
        this.start = start;
    }

    public String getOrderKey() {
        return orderKey;
    }
    public void setOrderKey(String orderKey) {
        this.orderKey = orderKey;
    }

    public String getOrderDir() {
        return orderDir;
    }
    public void setOrderDir(String orderDir) {
        this.orderDir = orderDir;
    }
}
