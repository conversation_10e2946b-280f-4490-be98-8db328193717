package cz.wincor.ovv.viewer.controller.api;

import cz.wincor.ovv.viewer.dto.VoucherDto;
import cz.wincor.ovv.viewer.service.RedemptionService;
import cz.wincor.ovv.viewer.xsd.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.net.ConnectException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/api/redemption")
public class RedemptionController {

    @Autowired
    private RedemptionService redemptionService;

    private static final Logger logger = LoggerFactory.getLogger(RedemptionController.class);

    private static final String TRANSACTION_TYPE_VALIDATION = "VALIDATION";
    private static final String TRANSACTION_TYPE_REVERSAL = "REVERSAL";
    private static final String TRANSACTION_TYPE_CHECK = "CHECK";

    @PostMapping(value = "/")
    public ResponseEntity find(@RequestBody VoucherDto voucher) {
        if (TRANSACTION_TYPE_REVERSAL.equals(voucher.getTransactionType())) {
            if (voucher.getAmount() == null) {
                voucher.setAmount(0L);
            }
            voucher.setRedemptionDate(new SimpleDateFormat("dd.MM.yyyy").format(new Date()));
        } else {
            if (!redemptionService.validateVoucherNumber(voucher.getVoucherNumber())) {
                return ResponseEntity.badRequest().body("Voucher number is not correct. Must contains only letters, numbers and be in range from 3 to 255");
            }

            if (voucher.getRedemptionDate().isEmpty()) {
                return ResponseEntity.badRequest().body("Redemption date must be set.");
            }
        }

        Response response = null;
        try {
            response = redemptionService.sendRequest(voucher);
            return ResponseEntity.ok(response);
        } catch (ConnectException e ) {
            logger.error("Error when trying to send a remote request", e);
            return ResponseEntity.badRequest().body("Connection refused. Please contact your administrator");
        } catch (IOException e) {
            logger.error("Error when trying to send a remote request", e);
            return ResponseEntity.badRequest().body("Cannot connect to server. Please contact your administrator");
        } catch (ParseException e) {
            logger.error("Wrong request data", e);
            return ResponseEntity.badRequest().body("Redemption date has bad structure.");
        }
    }


}
