package cz.wincor.ovv.viewer.controller.api;

import cz.wincor.ovv.viewer.controller.api.vo.DrawRequest;
import cz.wincor.ovv.viewer.controller.api.vo.DrawResult;
import cz.wincor.ovv.viewer.model.entity.ReportBatch;
import cz.wincor.ovv.viewer.model.entity.ReportFile;
import cz.wincor.ovv.viewer.model.search.BatchSearchCriteria;
import cz.wincor.ovv.viewer.service.BatchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/batch")
public class BatchController {

    @Autowired
    BatchService batchService;

    @GetMapping
    public DrawResult<ReportBatch> getReportBatchFiles(BatchSearchCriteria searchCriteria, DrawRequest drawRequest) {
        drawRequest.mapToSearchCriteria(searchCriteria);
        Page<ReportBatch> page = batchService.getBatchFileDetail(searchCriteria);
        return new DrawResult<>(drawRequest, page);
    }

    @GetMapping(value = "/find")
    public List<ReportFile> find(@RequestParam("text") String text) {
        return batchService.findByReportFileName(text);
    }

    @GetMapping(value = "/file")
    public DrawResult<ReportFile> getReportFiles(BatchSearchCriteria searchCriteria, DrawRequest drawRequest) {
        drawRequest.mapToSearchCriteria(searchCriteria);
        Page<ReportFile> page = batchService.getReportFile(searchCriteria);
        return new DrawResult<>(drawRequest, page);
    }
}
