package cz.wincor.ovv.viewer.controller;

import java.util.Collection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import cz.wincor.ovv.viewer.exception.NotFoundException;
import cz.wincor.ovv.viewer.exception.ValidationException;
import cz.wincor.ovv.viewer.validation.ValidationError;

/**
 * Common Exception Handlers.
 *
 * <AUTHOR>
 */
@ControllerAdvice
public class ExceptionHandlingAdvice {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * Handler for {@link NotFoundException}.
     */
    @ExceptionHandler(NotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    @ResponseBody
    public String handleNotFoundException(NotFoundException ex) {
        logger.info("HTTP 404: {}.", ex.getMessage());
        return ex.getMessage();
    }

    /**
     * Handler for {@link ValidationException}.
     * @param ex Caught {@link ValidationException}.
     */
    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public Collection<ValidationError> handleValidationException(ValidationException ex) {
        logger.info("HTTP 400: {}.", ex.getErrors());
        return ex.getErrors();
    }
}
