package cz.wincor.ovv.viewer.controller.api.vo;

import java.util.List;

import org.springframework.data.domain.Page;

import cz.wincor.ovv.viewer.dto.DtoPage;

/**
 * Value object wrapping result of a single DataTables draw request.
 *
 * <AUTHOR>
 * @param <T> Type of wrapped objects to be listed in a table.
 */
public class DrawResult<T> {

    private int draw;
    private long recordsTotal;
    private long recordsFiltered;
    private List<T> data;


    /**
     * Create new draw result from a {@link DrawRequest} and a {@link Page} with result data.
     * @param request Draw request as sent from the DataTables client. Never {@code null}.
     * @param page Page with result data to be shown in the DataTables. Never {@code null}.
     */
    public DrawResult(DrawRequest request, Page<T> page) {
        this.draw = request.getDraw();
        this.recordsTotal = page.getTotalElements();
        this.recordsFiltered = page.getTotalElements();
        this.data = page.getContent();
    }

    /**
     * Create new draw result from a {@link DrawRequest} and a {@link DtoPage} with result data.
     * @param request Draw request as sent from the DataTables client. Never {@code null}.
     * @param page Page with result data to be shown in the DataTables. Never {@code null}.
     */
    public DrawResult(DrawRequest request, DtoPage<T> page) {
        this.draw = request.getDraw();
        this.recordsTotal = page.getTotal();
        this.recordsFiltered = page.getTotal();
        this.data = page.getData();
    }

    //~ Plain getters & setters

    public int getDraw() {
        return draw;
    }
    public void setDraw(int draw) {
        this.draw = draw;
    }

    public long getRecordsTotal() {
        return recordsTotal;
    }
    public void setRecordsTotal(long recordsTotal) {
        this.recordsTotal = recordsTotal;
    }

    public long getRecordsFiltered() {
        return recordsFiltered;
    }
    public void setRecordsFiltered(long recordsFiltered) {
        this.recordsFiltered = recordsFiltered;
    }

    public List<T> getData() {
        return data;
    }
    public void setData(List<T> data) {
        this.data = data;
    }
}
