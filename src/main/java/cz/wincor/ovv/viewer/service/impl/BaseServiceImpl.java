package cz.wincor.ovv.viewer.service.impl;

import com.querydsl.core.types.Predicate;
import cz.wincor.ovv.viewer.model.UserRole;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.model.search.BaseSearchCriteria;
import cz.wincor.ovv.viewer.security.SecurityUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Common ancestor of service interface implementations containing some convenient methods.
 *
 * <AUTHOR>
 */
public class BaseServiceImpl {

    /**
     * Create page request object for paging from the specified search criteria.
     * @param searchCriteria Search criteria object with common paging parameters. Never {@code null}.
     * @return {@link PageRequest} or {@code null} if paging parameters are not specified.
     */
    protected PageRequest createPageRequest(BaseSearchCriteria searchCriteria) {
        if (searchCriteria.getPage() == null || searchCriteria.getSize() == null) {
            return null;
        }
        return PageRequest.of(searchCriteria.getPage(), searchCriteria.getSize(), getSorting(searchCriteria));
    }

    /**
     * Get {@link Sort} from input parameters
     *
     * @param searchCriteria
     * @return
     */
    protected Sort getSorting(BaseSearchCriteria searchCriteria) {
        if (searchCriteria.getSortBy() == null || searchCriteria.getSortBy().isEmpty()) {
            return Sort.unsorted();
        }
        return Sort.by(searchCriteria.getDirection(), searchCriteria.getSortBy().toArray(new String[0]));
    }

    /**
     * Use repository to find all result with defined predicate and search criteria, creates correct page or sort requests
     * @param repository
     * @param predicate
     * @param searchCriteria
     * @return {@link Page} of results, may be empty, never null
     */
    protected <T> Page<T> findAll(QuerydslPredicateExecutor<T> repository, Predicate predicate, BaseSearchCriteria searchCriteria) {
       PageRequest pageRequest = createPageRequest(searchCriteria);
       if (pageRequest != null) {
           return repository.findAll(predicate, pageRequest);
       } else {
           return new PageImpl<T>(makeList(repository.findAll(predicate, getSorting(searchCriteria))));
       }
    }

    /**
     * Make list from {@link Iterable}
     * @param iter
     * @return
     */
    private static <E> List<E> makeList(Iterable<E> iter) {
        List<E> list = new ArrayList<E>();
        for (E item : iter) {
            list.add(item);
        }
        return list;
    }

    /**
     * Apply common restrictions based on user privileges to the given search criteria.
     * @param searchCriteria Search criteria which should be processed. Never {@code null}.
     */
    protected void restrictSearchCriteria(BaseSearchCriteria searchCriteria) {
        if (!SecurityUtils.hasRole(UserRole.ADMIN)) {
            List<Long> usersStores = SecurityUtils.getLoggedUser().getStores().stream().map(Store::getId).collect(Collectors.toList());
            if (searchCriteria.getStoreIds() == null || searchCriteria.getStoreIds().isEmpty()) {
                searchCriteria.setStoreIds(usersStores);
            } else {
                // Filter only allowed stores
                List<Long> allowedStores = searchCriteria.getStoreIds()
                        .stream()
                        .filter(it -> usersStores.contains(it))
                        .collect(Collectors.toList());
                searchCriteria.setStoreIds(allowedStores);
            }
        }
    }
}
