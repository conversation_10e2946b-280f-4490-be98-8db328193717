package cz.wincor.ovv.viewer.service;

import cz.wincor.ovv.viewer.dto.VoucherDto;
import cz.wincor.ovv.viewer.xsd.Response;

import java.io.IOException;
import java.text.ParseException;

public interface RedemptionService {

    /**
     * Send request to WS
     *
     * @param voucherDto {@link VoucherDto}
     * @return Response
     * @throws IOException if request cannot be delivered or failed
     */
    Response sendRequest(VoucherDto voucherDto) throws IOException, ParseException;

    /**
     * Validation of voucher number
     *
     * @param voucherNumber number of voucher
     * @return result if voucher is valid
     */
    boolean validateVoucherNumber(String voucherNumber);
}
