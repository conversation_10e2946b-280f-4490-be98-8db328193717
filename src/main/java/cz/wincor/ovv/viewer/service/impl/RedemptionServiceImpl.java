package cz.wincor.ovv.viewer.service.impl;

import cz.wincor.ovv.viewer.dto.VoucherDto;
import cz.wincor.ovv.viewer.model.entity.User;
import cz.wincor.ovv.viewer.pojo.GeneralMessage;
import cz.wincor.ovv.viewer.pojo.HTTPMessage;
import cz.wincor.ovv.viewer.pojo.Message;
import cz.wincor.ovv.viewer.security.SecurityUtils;
import cz.wincor.ovv.viewer.service.RedemptionService;
import cz.wincor.ovv.viewer.service.Sender;
import cz.wincor.ovv.viewer.xsd.CountryCode;
import cz.wincor.ovv.viewer.xsd.Request;
import cz.wincor.ovv.viewer.xsd.Response;
import cz.wincor.ovv.viewer.xsd.TransType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.oxm.Marshaller;
import org.springframework.oxm.Unmarshaller;
import org.springframework.oxm.XmlMappingException;
import org.springframework.stereotype.Service;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class RedemptionServiceImpl implements RedemptionService {

    private static final Logger LOG = LoggerFactory
            .getLogger(RedemptionServiceImpl.class);
    /**
     * Whether a request to a remote host should be sent through the secure
     * channel or not
     */
    @Value("${remoteHost.request.sendSecure:true}")
    private boolean sendSecure;

    @Autowired
    protected Marshaller marshaller;

    @Autowired
    protected Unmarshaller unmarshaller;

    /**
     * Sender for unsecure online channel
     */
    @Autowired
    @Qualifier("httpSender")
    private Sender<byte[], byte[]> httpSender;

    /**
     * Sender for secure online channel
     */
    @Autowired
    @Qualifier("httpsSender")
    private Sender<byte[], byte[]> httpsSender;

    private final String POS_ID = "OVV-VIEWER";

    private final Pattern PATTERN = Pattern.compile("[a-zA-Z0-9]{3,255}");

    @Override
    public Response sendRequest(VoucherDto voucherDto) throws IOException, ParseException {
        LOG.info("Redeeming voucher : " + voucherDto.toString());
        User loggedUser = SecurityUtils.getLoggedUser();
        Request ovvRequest = new Request();

        SimpleDateFormat formatter = new SimpleDateFormat("dd.MM.yyyy");
        Date posDate = formatter.parse(voucherDto.getRedemptionDate());

        ovvRequest.setPosId((voucherDto.getTransactionType().equals("REVERSAL")) ? voucherDto.getDevice() : POS_ID);
        ovvRequest.setPosDatetime(convertDateToXmlGregorianCalendar(posDate));
        // we have only CZ so no other codes are expected
        ovvRequest.setPosCountryCode((short) 203);
        ovvRequest.setPosCurrencyCode((short) 203);
        if(voucherDto.getTransactionType().equals("VALIDATION")) {
            //generate pseudo stan to "simulate"
            ovvRequest.setStan(Math.toIntExact(Instant.now().toEpochMilli() % 100000));
        } else {
            ovvRequest.setStan(voucherDto.getStan());
        }
        ovvRequest.setCountryCode(CountryCode.fromValue(voucherDto.getStore().getCountryCode().name()));
        ovvRequest.setPartnerId(voucherDto.getStore().getPartnerId());
        ovvRequest.setPaymentPlace(voucherDto.getStore().getSiteCode());
        ovvRequest.setTransType(TransType.valueOf(voucherDto.getTransactionType()));
        ovvRequest.setVoucherNumber(voucherDto.getVoucherNumber());
        ovvRequest.setOperator(loggedUser.getUsername());
        ovvRequest.setAmount(voucherDto.getAmount());
        ovvRequest.setPosId(voucherDto.getDevice());
        ovvRequest.setManualRedemption(Boolean.TRUE);

        Message<byte[]> request = null;
        HTTPMessage<byte[]> response;
            request = new GeneralMessage<>(POS_ID, createRawOvvRequest(ovvRequest));

        if (sendSecure) {
            LOG.debug("Client {} - Trying to use HTTPS channel to the remote host.", request.getClientId());
            response = (HTTPMessage<byte[]>) httpsSender.send(request);
        } else {
            LOG.debug("Client {} - Trying to use HTTP channel to the remote host.", request.getClientId());
            response = (HTTPMessage<byte[]>) httpSender.send(request);
        }

        HttpStatus httpStatus = null;
        try {
            httpStatus = HttpStatus.valueOf(response.getHttpStatus());
            LOG.debug("Client {} - Received response from the remote host with HTTP status {} - {}",
                    request.getClientId(), httpStatus.value(), httpStatus.getReasonPhrase());
        } catch (final IllegalArgumentException e) {
            throw new IOException(
                    "Client " + request.getClientId() + "Received no HTTP status from the remote host");
        }
        // Test if a received response code is 200 (otherwise throw an
        // exception)
        if (!HttpStatus.OK.equals(httpStatus)) {
            throw new IOException("Client " + request.getClientId()
                    + "Received HTTP status code is not successful (expected=" + HttpStatus.OK.value() + ", actual="
                    + httpStatus.value() + " - " + httpStatus.getReasonPhrase() + ")");
        }

        return parseOvvResponse(response);
    }

    public boolean validateVoucherNumber(String voucherNumber) {
        Matcher m = PATTERN.matcher(voucherNumber);
        return m.matches();
    }


    private XMLGregorianCalendar convertDateToXmlGregorianCalendar(Date inputDate) {

        GregorianCalendar c = new GregorianCalendar();
        c.setTime(inputDate);
        try {
            return DatatypeFactory.newInstance().newXMLGregorianCalendar(c);
        } catch (DatatypeConfigurationException e) {
            throw new IllegalArgumentException("Cannot convert date " + inputDate + " into XMLGregorianCalendar", e);
        }
    }

    /**
     * Marshall {@link Request} into array of bytes
     *
     * @param request
     *            to be marshalled
     * @return array of bytes
     * @throws IOException
     *             in a case of marshalling error
     */
    private byte[] createRawOvvRequest(Request request) throws IOException {
        final ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        marshaller.marshal(request, new StreamResult(byteArrayOutputStream));
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * Method for parsing an OVV response into {@link Response}
     *
     * @param response message
     * @throws IOException
     * @throws XmlMappingException
     * @return {@link Response} parsed object
     */
    private Response parseOvvResponse(Message<byte[]> response) throws IOException, XmlMappingException {
        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(response.getData());
        final Response requestObject = (Response) unmarshaller
                .unmarshal(new StreamSource(byteArrayInputStream));
        return requestObject;
    }
}
