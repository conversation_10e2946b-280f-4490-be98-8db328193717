package cz.wincor.ovv.viewer.service;

import java.util.List;

import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.enums.StoreSearchScope;
import cz.wincor.ovv.viewer.model.entity.CashRegister;
import cz.wincor.ovv.viewer.model.entity.Store;

/**
 * Service interface for stores.
 *
 * <AUTHOR>
 */
public interface StoreService {

    List<Store> findStore(String store, CountryEnum country, StoreSearchScope searchScope);

    List<CashRegister> findStoreCashRegisters(String siteCode, String number);
}
