package cz.wincor.ovv.viewer.service.impl;

import com.querydsl.core.BooleanBuilder;
import cz.wincor.ovv.viewer.bootstrap.ConfigParams;
import cz.wincor.ovv.viewer.exception.LargeExportException;
import cz.wincor.ovv.viewer.exception.NotFoundException;
import cz.wincor.ovv.viewer.model.entity.QTransaction;
import cz.wincor.ovv.viewer.model.entity.Transaction;
import cz.wincor.ovv.viewer.model.search.TransactionSearchCriteria;
import cz.wincor.ovv.viewer.repository.TransactionRepository;
import cz.wincor.ovv.viewer.service.ViewerService;
import cz.wincor.ovv.viewer.utils.PropertyUtils;
import cz.wincor.ovv.viewer.utils.RowBuilder;
import cz.wincor.ovv.viewer.utils.StoreUtil;
import cz.wincor.ovv.viewer.xsd.CountryCode;
import java.io.*;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.function.Consumer;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

@Service
@Transactional(readOnly = true)
public class ViewerServiceImpl extends BaseServiceImpl implements ViewerService {

    private static final Logger LOG = LoggerFactory.getLogger(ViewerServiceImpl.class);

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private ConfigParams configParams;

    @Override
    public Page<Transaction> getTransactions(TransactionSearchCriteria searchCriteria) {
        Assert.notNull(searchCriteria, "Search criteria must be specified.");
        LOG.info("Getting transactions : " + searchCriteria);
        long start = System.currentTimeMillis();
        BooleanBuilder predicate = new BooleanBuilder();
        if (searchCriteria.getDateTimeFrom() != null) {
            predicate.and(QTransaction.transaction.serverDateTime.goe(searchCriteria.getDateTimeFrom()));
        } else if (StringUtils.isBlank(searchCriteria.getVoucherNumber())) {
            // if voucher number is blank, set max limit to history
            if (searchCriteria.getDateTimeTo() != null) {
                predicate.and(QTransaction.transaction.serverDateTime.goe(searchCriteria.getDateTimeTo().minusDays(configParams.getTransactionSearchTimeLimit())));
            } else {
                predicate.and(QTransaction.transaction.serverDateTime.goe(LocalDateTime.now().minusDays(configParams.getTransactionSearchTimeLimit())));
            }
        }
        if (searchCriteria.getDateTimeTo() != null) {
            predicate.and(QTransaction.transaction.serverDateTime.lt(searchCriteria.getDateTimeTo()));
        }
        if (searchCriteria.getCountryCode() != null) {
            predicate.and(QTransaction.transaction.countryCode.eq(searchCriteria.getCountryCode().toString()));
        }
        if (StringUtils.isNotBlank(searchCriteria.getDeviceId())) {
            predicate.and(QTransaction.transaction.deviceId.eq(searchCriteria.getDeviceId()));
        }
        if (searchCriteria.getStan() != null) {
            predicate.and(QTransaction.transaction.stan.eq(searchCriteria.getStan()));
        }
        if (searchCriteria.getType() != null) {
            predicate.and(QTransaction.transaction.transactionType.eq(searchCriteria.getType().toString()));
        }
        if (StringUtils.isNotBlank(searchCriteria.getVoucherNumber())) {
            predicate.and(QTransaction.transaction.voucherNumber.eq(searchCriteria.getVoucherNumber()));
        }
        if (searchCriteria.getIssuer() != null && searchCriteria.getIssuer().length > 0) {
            predicate.and(QTransaction.transaction.ovvIssuer.in(searchCriteria.getIssuer()));
        }
        if (searchCriteria.getValidationHost() != null) {
            predicate.and(QTransaction.transaction.validationHost.eq(searchCriteria.getValidationHost().toString()));
        }
        if (searchCriteria.getResultCode() != null && searchCriteria.getResultCode().length > 0) {
            predicate.and(QTransaction.transaction.resultCode.in(searchCriteria.getResultCode()));
        }
        if (searchCriteria.getExpirationYear() != null) {
            LocalDateTime from = LocalDateTime.now().with(LocalTime.MIN).with(searchCriteria.getExpirationYear()).with(TemporalAdjusters.firstDayOfYear());
            LocalDateTime to = LocalDateTime.now().with(LocalTime.MAX).with(searchCriteria.getExpirationYear()).with(TemporalAdjusters.lastDayOfYear());

            predicate.and(QTransaction.transaction.expirationDate.isNotNull());
            predicate.and(QTransaction.transaction.expirationDate.between(from, to));
        }
        if (searchCriteria.getTrainingMode() != null) {
            predicate.and(QTransaction.transaction.trainingMode.eq(searchCriteria.getTrainingMode()));
        }
        if (searchCriteria.getOfflineMode() != null) {
            predicate.and(QTransaction.transaction.offlineMode.eq(searchCriteria.getOfflineMode()));
        }
        if (searchCriteria.getManualRedemption() != null) {
            predicate.and(QTransaction.transaction.manualRedemption.eq(searchCriteria.getManualRedemption()));
        }
        if (StringUtils.isNotBlank(searchCriteria.getSiteCode())) {
            CountryCode countryCode = StoreUtil.getCountryCode(searchCriteria.getSiteCode());
            if (countryCode != null) {
                /**
                 * Some transactions have missing the first digit in payment place (which
                 * indicates a country), so we have to search for substring of payment place and
                 * explicit country
                 */
                BooleanBuilder innerPredicate1 = new BooleanBuilder();
                innerPredicate1.and(QTransaction.transaction.countryCode.eq(countryCode.toString()));
                innerPredicate1.and(QTransaction.transaction.paymentPlace.eq(searchCriteria.getSiteCode().substring(1)));

                BooleanBuilder innerPredicate2 = new BooleanBuilder();
                innerPredicate2.and(QTransaction.transaction.paymentPlace.eq(searchCriteria.getSiteCode()));

                predicate.and(new BooleanBuilder(innerPredicate1).or(innerPredicate2));
            } else {
                predicate.and(QTransaction.transaction.paymentPlace.eq(searchCriteria.getSiteCode()));
            }

        }

        Page<Transaction> result = findAll(transactionRepository, predicate, searchCriteria);
        long end = System.currentTimeMillis();
        LOG.info("Got transactions in " + (end - start) + " ms.");
        return result;
    }

    @Override
    public Transaction getTransaction(long id) throws NotFoundException {
        LOG.info("Getting transactions with id  : " + id);
        long start = System.currentTimeMillis();
        Transaction transaction = transactionRepository.findById(id).orElseThrow(() -> {
                    LOG.info("Transaction with id " + id + " not found");
                    return new NotFoundException("Transaction", id);
                }
        );
        long end = System.currentTimeMillis();
        LOG.info("Got transaction in " + (end - start) + " ms.");
        return transaction;
    }

    @Override
    public void getTransactionXls(TransactionSearchCriteria searchCriteria, File outFile) {
        LOG.info("Getting transactions xls for : " + searchCriteria.toString());
        long start = System.currentTimeMillis();
        SXSSFWorkbook workbook = new SXSSFWorkbook(SXSSFWorkbook.DEFAULT_WINDOW_SIZE);
        int count = 0;

        try {
            SXSSFSheet sheet = workbook.createSheet("Transactions");
            RowBuilder rowBuilder = new RowBuilder(sheet, workbook);

            rowBuilder.createRow()
                    .addCell("DATE, TIME").setWidth(19) //1
                    .addCell("POS DATE, TIME").setWidth(19) //2
                    .addCell("COUNTRY")                 //3
                    .addCell("DEVICE")                  //4
                    .addCell("MANUAL REDEMPTION")       //5
                    .addCell("STAN")                    //6
                    .addCell("PAYMENT PLACE").setWidth(15) //7
                    .addCell("TYPE")                    //8
                    .addCell("AMOUNT")                  //9
                    .addCell("S/N").setWidth(29)        //10
                    .addCell("ISSUER")                  //11
                    .addCell("CATEGORY")                //12
                    .addCell("OFFLINE")                 //13
                    .addCell("TRAINING MODE")           //14
                    .addCell("RESULT").setWidth(22)     //15
                    .addCell("Additional Info").setWidth(35);    //16
            rowBuilder.freezeHeaderRow();

            int transactionCount = configParams.getExportBatchSize();
            while (transactionCount == configParams.getExportBatchSize()) {
                transactionCount = fillXlsTransactions(count, configParams.getExportBatchSize(), searchCriteria, tr -> {
                    long amount = tr.getAmount();
                    if ("REVERSAL".equals(tr.getTransactionType())) {
                        amount = -amount;
                    }
                    rowBuilder.createRow()
                            .addDateTime(tr.getServerDateTime().withNano(0))    //1
                            .addDateTime(tr.getDeviceDateTime().withNano(0))    //2
                            .addCell(tr.getCountryCode())                       //3
                            .addCell(tr.getDeviceId())                          //4
                            .addCell(PropertyUtils.booleanToYesNo(tr.getManualRedemption()))    //5
                            .addCell(tr.getStan())                              //6
                            .addCell(tr.getPaymentPlace())                      //7
                            .addCell(tr.getTransactionType())                   //8
                            .addCell(((double) amount) / 100)                   //9
                            .addCell(tr.getVoucherNumber())                     //10
                            .addCell(tr.getOvvIssuer())                         //11
                            .addCell(tr.getCategory())                          //12
                            .addCell(PropertyUtils.smallBooleanToYesNo(tr.isOfflineMode()))     //13
                            .addCell(PropertyUtils.booleanToYesNo(tr.getTrainingMode()))     //14
                            .addCell(tr.getResultCode())                        //15
                            .addCell(tr.getErrorDescription());                 //16
                });

                count += transactionCount;

                if (count >= configParams.getMaxExportSize()) {
                    throw new LargeExportException();
                }
            }

            rowBuilder.setAutoFilter();

            try (OutputStream out = new BufferedOutputStream(new FileOutputStream(outFile))) {
                workbook.write(out);
            }
        } catch (IOException e) {
            LOG.error("Problem writing file", e);
            throw new RuntimeException(e);
        } finally {
            workbook.dispose();
            long end = System.currentTimeMillis();
            LOG.info("Got transaction xls in " + (end - start) + " ms.");
        }
    }

    private int fillXlsTransactions(int from, int pageSize, TransactionSearchCriteria searchCriteria,
            Consumer<Transaction> transactionConsumer) {
        searchCriteria.setPage(from / pageSize);
        searchCriteria.setSize(pageSize);

        Page<Transaction> transactions = getTransactions(searchCriteria);

        transactions.forEach(transactionConsumer);
        return transactions.getNumberOfElements();
    }
}
