package cz.wincor.ovv.viewer.service.impl;

import cz.wincor.ovv.viewer.service.Sender;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.stereotype.Component;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URL;
import java.security.KeyStore;

@Component("httpsSender")
public class HTTPSSenderImpl extends HTTPSenderImpl implements
        ResourceLoaderAware, Sender<byte[], byte[]> {

    private static final Logger LOG = LoggerFactory
            .getLogger(HTTPSSenderImpl.class);

    /**
     * Keystore password
     */
    @Value("${remoteHost.keyStore.password:Wincor2015}")
    private String keyStorePassword;

    /**
     * Path to keystore
     */
    @Value("${remoteHost.keyStore.path:}")
    private String keyStorePath;

    /**
     * jceks The proprietary keystore implementation provided by the SunJCE
     * provider.<br />
     * jks The proprietary keystore implementation provided by the SUN provider.<br />
     * pkcs12 The transfer syntax for personal identity information as defined
     * in PKCS #12.
     */
    @Value("${remoteHost.keyStore.type:JKS}")
    private String keyStoreType;

    /**
     * SSL Supports some version of SSL; may support other versions<br />
     * SSLv2 Supports SSL version 2 or later; may support other versions<br />
     * SSLv3 Supports SSL version 3; may support other versions<br />
     * TLS Supports some version of TLS; may support other versions<br />
     * TLSv1 Supports RFC 2246: TLS version 1.0 ; may support other versions<br />
     * TLSv1.1 Supports RFC 4346: TLS version 1.1 ; may support other versions<br />
     * TLSv1.2 Supports RFC 5246: TLS version 1.2 ; may support other versions
     */
    @Value("${remoteHost.security.algorithm:TLS}")
    private String securityAlgorithm = "TLS";

    SSLSocketFactory sSLSocketFactory;

    /**
     * Initialize a SSLSocketFactory as a singleton
     *
     * @throws Exception
     */
    @PostConstruct
    private void afterPropertiesSet() throws Exception {
        this.sSLSocketFactory = createSSLSocketFactory();
        LOG.info("HTTPS sender initialized:\n {}, {}", super.toString(), toString());
    }

    /**
     * Override a parent, return {@link HttpsURLConnection}
     */
    @Override
    protected HttpURLConnection createHttpURLConnection(URL obj)
            throws IOException, ProtocolException {
        final HttpsURLConnection urlConnection = (HttpsURLConnection) super
                .createHttpURLConnection(obj);
        urlConnection.setSSLSocketFactory(sSLSocketFactory);
        return urlConnection;
    }

    /**
     * Initialize {@link SSLSocketFactory} which is important for secure HTTPS channel to a remote host
     * @return
     * @throws IllegalStateException
     */
    protected SSLSocketFactory createSSLSocketFactory()
            throws IllegalStateException {
        if (keyStorePath == null || keyStorePath.isEmpty()) {
            return (SSLSocketFactory)SSLSocketFactory.getDefault();
        }
        String trustStorePath = keyStorePath;
        // Fill in the correct prefix
        if (keyStorePath != null && !keyStorePath.startsWith("classpath:")) {
            trustStorePath = "file:" + keyStorePath;
        }
        try (final InputStream instream = resourceLoader.getResource(
                trustStorePath).getInputStream()) {
            final KeyStore trustStore = KeyStore.getInstance(keyStoreType);
            trustStore.load(instream, keyStorePassword.toCharArray());
            final TrustManagerFactory tmf = TrustManagerFactory
                    .getInstance(TrustManagerFactory.getDefaultAlgorithm());
            tmf.init(trustStore);
            final SSLContext ctx = SSLContext.getInstance(securityAlgorithm);
            ctx.init(null, tmf.getTrustManagers(), null);
            return ctx.getSocketFactory();
        } catch (final Exception e) {
            throw new IllegalStateException(
                    "Cannot initialize SSLSocketFactory", e);
        }
    }

    /**
     * @param keyStorePassword
     *            the keyStorePassword to set
     */
    public void setKeyStorePassword(String keyStorePassword) {
        this.keyStorePassword = keyStorePassword;
    }

    /**
     * @param keyStorePath
     *            the keyStorePath to set
     */
    public void setKeyStorePath(String keyStorePath) {
        this.keyStorePath = keyStorePath;
    }

    /**
     * @param keyStoreType the keyStoreType to set
     */
    public void setKeyStoreType(String keyStoreType) {
        this.keyStoreType = keyStoreType;
    }

    /**
     * @param sSLSocketFactory
     *            the sSLSocketFactory to set
     */
    public void setsSLSocketFactory(SSLSocketFactory sSLSocketFactory) {
        this.sSLSocketFactory = sSLSocketFactory;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return "keyStorePath=" + keyStorePath
                + ", keyStoreType=" + keyStoreType + ", securityAlgorithm="
                + securityAlgorithm;
    }

}
