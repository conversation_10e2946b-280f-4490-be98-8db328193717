package cz.wincor.ovv.viewer.service.impl;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.enums.StoreSearchScope;
import cz.wincor.ovv.viewer.exception.ValidationException;
import cz.wincor.ovv.viewer.model.UserRole;
import cz.wincor.ovv.viewer.model.entity.CashRegister;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.model.entity.User;
import cz.wincor.ovv.viewer.repository.CashRegisterRepository;
import cz.wincor.ovv.viewer.repository.StoreRepository;
import cz.wincor.ovv.viewer.security.SecurityUtils;
import cz.wincor.ovv.viewer.service.StoreService;
import cz.wincor.ovv.viewer.utils.CashRegisterNumberComparator;

@Service
@Transactional(rollbackFor = ValidationException.class)
public class StoreServiceImpl extends BaseServiceImpl implements StoreService {

    private static final Logger LOG = LoggerFactory.getLogger(StoreServiceImpl.class);

    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private CashRegisterRepository cashRegisterRepository;

    @Value("${app.store.searchSize}")
    private int storeSearchSize;

    @Override
    public List<Store> findStore(String store, CountryEnum country, StoreSearchScope searchScope) {
        LOG.info("Finding stores with search string : \'" + store + "\', search scope is : " + searchScope.name());
        User loggedUser = SecurityUtils.getLoggedUser();
        // Admin will see always all stores
        if (StoreSearchScope.USER.equals(searchScope) && !loggedUser.getRole().equals(UserRole.ADMIN)) {
            return storeRepository.findUserStoresBySiteCodeOrName(loggedUser.getId(), "%" + store + "%",
                    PageRequest.of(0, storeSearchSize));
        } else {
            if (country == null) {
                return storeRepository.findAllBySiteCodeOrName("%" + store + "%", PageRequest.of(0, storeSearchSize));
            } else {
                return storeRepository.findAllBySiteCodeOrNameAndCountry("%" + store + "%", country,
                        PageRequest.of(0, storeSearchSize));
            }
        }
    }

    @Override
    public List<CashRegister> findStoreCashRegisters(String siteCode, String number) {
        List<CashRegister> result = cashRegisterRepository.findAllBySiteCodeAndNumberFilter(siteCode, number);
        Collections.sort(result, new CashRegisterNumberComparator());
        return result;
    }

}
