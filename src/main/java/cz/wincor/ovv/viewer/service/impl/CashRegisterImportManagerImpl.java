package cz.wincor.ovv.viewer.service.impl;

import com.google.common.io.Files;
import com.opencsv.bean.CsvToBean;
import cz.wincor.ovv.viewer.bootstrap.AppConfig;
import cz.wincor.ovv.viewer.model.entity.CashRegister;
import cz.wincor.ovv.viewer.pojo.CashRegisterImportEntry;
import cz.wincor.ovv.viewer.repository.CashRegisterRepository;
import cz.wincor.ovv.viewer.service.CashRegisterImportManager;
import cz.wincor.ovv.viewer.utils.CsvUtil;
import cz.wincor.ovv.viewer.utils.Util;
import cz.wincor.ovv.viewer.utils.sftp.SftpClient;
import cz.wincor.ovv.viewer.utils.sftp.SftpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.io.IOException;
import java.io.Reader;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Spliterator;

/**
 * Cash register definitions importer.
 *
 * <AUTHOR>
 *
 */
@Component
public class CashRegisterImportManagerImpl implements CashRegisterImportManager {

    private static final Logger LOG = LoggerFactory.getLogger(CashRegisterImportManagerImpl.class);

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private CashRegisterRepository cashRegisterRepository;

    @Autowired
    private AppConfig.Factory<SftpClient> sftpClientFactory;

    @Value("${cashRegisterImport.sftp.folder}")
    private String cashRegisterSftpFolder;

    @Value("${cashRegisterImport.sftp.archiveFolder}")
    private String cashRegisterSftpArchiveFolder;

    @Value("${cashRegisterImport.downloadFolder}")
    private String cashRegisterDownloadFolder;

    @Value("${cashRegisterImport.archiveFolder}")
    private String cashRegisterArchiveFolder;

    @Value("${cashRegisterImport.batchSize:1000}")
    private Integer cashRegisterBatchSize;

    /**
     * Cash register list update task. Will erase all entries and replace them with
     * new one from files on sftp.
     *
     * @throws Exception
     */
    @Scheduled(cron = "${cron.cashRegisterExpression}")
    public void updateCashRegisters() throws Exception {
        LOG.info("Updating cash register definitions");
        // create temp directory
        File downloadFolder = new File(cashRegisterDownloadFolder);
        // download all files and remotelly archive
        try (SftpClient sftpClient = sftpClientFactory.getObject()) {
            SftpUtil.downloadFilesFromFolder(cashRegisterSftpFolder, downloadFolder, sftpClient);
            SftpUtil.remotellyArchiveFiles(cashRegisterSftpFolder, cashRegisterSftpArchiveFolder, sftpClient);
            // check files
            if (downloadFolder.list().length > 0) {
                File[] filesToImport = downloadFolder.listFiles();
                // clean up non active left by previous erroneous runs
                cleanUp();
                for (File file : filesToImport) {
                    LOG.info("Importing entries from : " + file.getName());
                    importEntries(file);
                }
                // remove old active entries set rest (newly imported) to active
                finishImport();
                LOG.info("Updated cash register definitions");
            }
        } catch (Exception e) {
            cleanUp();
            LOG.error("Unable to update cash register definitions", e);
        } finally {
            // locally archive all files
            try {
                Util.archiveLocally(downloadFolder, cashRegisterArchiveFolder);
            } catch (IOException e) {
                LOG.error("Unable to delete temporary folder");
            }
        }

    }

    /**
     * Import on Csv file with cash register import entries. The file is read as
     * stream and committed in specifically sized batches.
     *
     * @param file
     */
    public void importEntries(File file) {
        long processedEntries = 0;
        int batchSize = cashRegisterBatchSize;
        try (Reader fileReader = Files.newReader(file, Charset.defaultCharset())) {
            CsvToBean<CashRegisterImportEntry> beanConverter = CsvUtil.getBeanConverter(fileReader);
            Spliterator<CashRegisterImportEntry> split = Util.getSpliterator(beanConverter.iterator());

            while (true) {
                List<CashRegisterImportEntry> batch = new ArrayList<>(batchSize);
                for (int i = 0; i < batchSize && split.tryAdvance(batch::add); i++) {
                }
                ;
                if (batch.isEmpty())
                    break;
                processedEntries += transactionTemplate.execute(status -> saveBatch(batch));
                LOG.debug(processedEntries + " entries processed.");

            }
        } catch (Exception e) {
            LOG.error("Unable to parse file : " + file.getAbsolutePath(), e);
            throw new RuntimeException("Unable to parse file : " + file.getAbsolutePath());
        }

        LOG.info("Total " + processedEntries + " entries processed.");
    }

    /**
     * All transfered. Delete all active and the rest switch to active.
     */
    private void finishImport() {
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {

            @Override
            protected void doInTransactionWithoutResult(TransactionStatus arg0) {
                long updated = cashRegisterRepository.deleteActive();
                LOG.debug(updated + " active entries deleted.");
                updated = cashRegisterRepository.updateActive(true);
                LOG.debug(updated + " entries activated.");
            }
        });

    }

    /**
     * Delete are not active entries. Probably from previous erroneous run.
     */
    private void cleanUp() {
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {

            @Override
            protected void doInTransactionWithoutResult(TransactionStatus arg0) {
                long deleted = cashRegisterRepository.deleteNonActive();
                LOG.debug(deleted + " non-active entries deleted.");
            }
        });
    }

    /**
     * Store batch of {@link CashRegisterImportEntry} entries to DB.
     *
     * @param chunk
     * @return
     */
    private int saveBatch(List<CashRegisterImportEntry> chunk) {
        int counter = 0;
        for (CashRegisterImportEntry cashRegisterImportEntry : chunk) {
            CashRegister cashRegister = new CashRegister();
            cashRegister.setSiteCode(cashRegisterImportEntry.getSiteCode());
            cashRegister.setNumber(cashRegisterImportEntry.getCashRegisterNumber());
            cashRegister.setImportDateTime(LocalDateTime.now());
            cashRegister.setActive(false);
            cashRegisterRepository.save(cashRegister);
            LOG.debug("Persisting : " + cashRegisterImportEntry.toString());
            counter++;
        }
        return counter;
    }

}
