package cz.wincor.ovv.viewer.service.impl;

import cz.wincor.ovv.viewer.pojo.HTTPMessage;
import cz.wincor.ovv.viewer.pojo.Message;
import cz.wincor.ovv.viewer.service.Sender;
import cz.wincor.ovv.viewer.utils.GWLogger;
import cz.wincor.ovv.viewer.utils.HTTPUtil;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * A simple HTTP implementation of a {@link Sender}
 *
 * <AUTHOR>
 *
 */
@Component("httpSender")
public class HTTPSenderImpl implements Sender<byte[], byte[]>,
        ResourceLoaderAware {

    private static final Logger LOG = LoggerFactory
            .getLogger(HTTPSenderImpl.class);
    /**
     * Connect timeout which will be used for connecting to a remote server<br />
     * Sets a specified timeout value, in milliseconds, to be used when opening
     * a communications link to the resource referenced by this URLConnection.
     * If the timeout expires before the connection can be established, a
     * java.net.SocketTimeoutException is raised. A timeout of zero is
     * interpreted as an infinite timeout.
     */
    @Value("${remoteHost.request.connectTimeout:3000}")
    private int connectTimeout;
    /**
     * Content-Type
     */
    @Value("${remoteHost.request.contentType:text/xml; charset=utf-8}")
    private String contentType;
    /**
     * URL of the Validation Server
     */
    @Value("${remoteHost.request.url}")
    private String requestUrl;
    protected ResourceLoader resourceLoader;
    /**
     * Http header param User-Agent which will be used in a request to a remote
     * server
     */
    @Value("${remoteHost.request.userAgent:OVVViewer}")
    private String userAgent;

    /**
     * Create a new instance of {@link HttpURLConnection}
     *
     * @param obj
     *            URL
     * @return
     * @throws IOException
     * @throws ProtocolException
     */
    protected HttpURLConnection createHttpURLConnection(final URL obj)
            throws IOException, ProtocolException {
        final HttpURLConnection conn = (HttpURLConnection) obj.openConnection();
        conn.setRequestMethod(HttpMethod.POST.name());
        conn.setRequestProperty("User-Agent", userAgent);
        conn.setRequestProperty("Content-Type", contentType);
        conn.setConnectTimeout(connectTimeout);
        conn.setDoOutput(true);
        return conn;
    }

    /**
     * Send a request message to a remote host via HTTP protocol
     *
     * @param request
     *            message
     */
    @Override
    public HTTPMessage<byte[]> send(Message<byte[]> request) throws IOException {
        // Create a HTTP connection from URL
        final URL url = new URL(requestUrl);
        LOG.debug("Client {} - Creating HTTP(S) connection to {}",
                request.getClientId(), url.toString());
        final HttpURLConnection conn = createHttpURLConnection(url);

        // Send all data
        try (final DataOutputStream wr = new DataOutputStream(
                conn.getOutputStream())) {
            wr.write(request.getData());
            wr.flush();
        }
        GWLogger.logHexDebug("Client " + request.getClientId() + " --> OVV ("
                + requestUrl + "), REQUEST MESSAGE:", request.getData(), LOG);
        // Get response code
        final int httpStatus = conn.getResponseCode();
        final String contentType = conn.getContentType();

        final String charsetFromContentType = HTTPUtil.getCharsetFromContentType(contentType);
        byte[] byteData = new byte[0];
        // Read all incoming data
        try (InputStream is = conn.getInputStream()) {
            if (charsetFromContentType != null) {
                byteData = IOUtils.toString(is, Charset.forName(charsetFromContentType)).getBytes();
            } else {
                byteData = IOUtils.toString(is, StandardCharsets.UTF_8).getBytes();
            }
        }
        final HTTPMessage<byte[]> response = new HTTPMessage<>(
                request.getClientId(), byteData);
        response.setHttpStatus(httpStatus);
        GWLogger.logHexDebug("Client " + request.getClientId() + "<-- OVV "
                + url.getQuery() + ", RESPONSE MESSAGE:", response.getData(), LOG);
        return response;
    }

    /**
     * @param connectTimeout
     *            the connectTimeout to set
     */
    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    /**
     * @param requestUrl
     *            the requestUrl to set
     */
    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }

    @Override
    public void setResourceLoader(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;

    }

    /*
     * (non-Javadoc)
     *
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return "contentType=" + contentType + ", requestUrl=" + requestUrl
                + ", userAgent=" + userAgent;
    }

}
