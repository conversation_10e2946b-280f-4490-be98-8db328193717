package cz.wincor.ovv.viewer.service;

import cz.wincor.ovv.viewer.model.entity.ReportBatch;
import cz.wincor.ovv.viewer.model.entity.ReportFile;
import cz.wincor.ovv.viewer.model.search.BatchSearchCriteria;
import cz.wincor.ovv.viewer.model.search.TransactionSearchCriteria;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * Service interface for batch data viewing functionality.
 *
 * <AUTHOR>
 */
public interface BatchService {

    /**
     * Get transactions matching the specified search criteria.
     *
     * @param searchCriteria {@link TransactionSearchCriteria} which can be used to restrict results
     * of the search and apply paging and sorting. Never {@code null}.
     * @return Page with found batch files.
     */
    Page<ReportBatch> getBatchFileDetail(BatchSearchCriteria searchCriteria);


    /**
     * @param text search text
     * @return List with Report Files {@link ReportFile}
     */
    List<ReportFile> findByReportFileName(String text);


    /**
     * @param searchCriteria {@link TransactionSearchCriteria} which can be used to restrict results
     * @return Page with found report files.
     */
    Page<ReportFile> getReportFile(BatchSearchCriteria searchCriteria);
}
