package cz.wincor.ovv.viewer.service;

import org.springframework.security.access.annotation.Secured;
import org.springframework.security.core.userdetails.UserDetailsService;

import cz.wincor.ovv.viewer.dto.DtoPage;
import cz.wincor.ovv.viewer.dto.PasswordChangeRequest;
import cz.wincor.ovv.viewer.dto.UserDto;
import cz.wincor.ovv.viewer.exception.NotFoundException;
import cz.wincor.ovv.viewer.exception.ValidationException;
import cz.wincor.ovv.viewer.model.entity.User;
import cz.wincor.ovv.viewer.model.search.UserSearchCriteria;

/**
 * User management service interface.
 *
 * <AUTHOR>
 */
public interface UserService extends UserDetailsService {

    /**
     * Get {@link User} with the given ID as {@link UserDTO}
     * @param userId User ID.
     * @return Existing user. Never null.
     * @throws NotFoundException if no user with the given ID exists.
     */
    @Secured({"ROLE_ADMIN", "ROLE_STORE_MANAGER"})
    UserDto getUserById(long userId) throws NotFoundException;

    /**
     * Get all existing {@link User Users} which meet a specific criteria
     * @param searchCriteria
     * @return Page of users. Can be empty, never null.
     */
    @Secured({"ROLE_ADMIN", "ROLE_STORE_MANAGER"})
    DtoPage<UserDto> getUsers(UserSearchCriteria searchCriteria);

    /**
     * Create new {@link User} and return it as {@link UserDTO}
     * @param user User DTO with populated user details. Never null.
     * @return Newly created user. Never null.
     * @throws ValidationException if the user can not be created due to validation errors.
     */
    @Secured({"ROLE_ADMIN", "ROLE_STORE_MANAGER"})
    UserDto createUser(UserDto user) throws ValidationException;

    /**
     * Update existing {@link User}
     * @param user User DTO with populated user details. Never null.
     * @return Updated user. Never null.
     * @throws ValidationException if the user can not be updated due to validation errors.
     * @throws NotFoundException if the user does not exist
     */
    @Secured({"ROLE_ADMIN", "ROLE_STORE_MANAGER"})
    UserDto updateUser(UserDto user) throws NotFoundException, ValidationException;

    /**
     * Change password of an existing {@link User}.
     * @param userId ID of the user whose password should be changed.
     * @param passwordChangeRequest DTO containing an old and new passwords.
     * @throws ValidationException if the entered old password does not match the current password
     * or if the new password is not strong enough.
     */
    void changePassword(long userId, PasswordChangeRequest passwordChangeRequest)
            throws ValidationException, NotFoundException;

    UserDto getLoggedUser();

    void expiredPasswordChange(cz.wincor.ovv.viewer.controller.api.PasswordChangeRequest passwordChangeRequest) throws ValidationException, NotFoundException;
}
