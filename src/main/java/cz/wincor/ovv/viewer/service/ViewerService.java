package cz.wincor.ovv.viewer.service;

import org.springframework.data.domain.Page;

import cz.wincor.ovv.viewer.exception.NotFoundException;
import cz.wincor.ovv.viewer.model.entity.Transaction;
import cz.wincor.ovv.viewer.model.search.TransactionSearchCriteria;

import java.io.File;

/**
 * Service interface for data viewing functionality.
 *
 * <AUTHOR>
 */
public interface ViewerService {

    /**
     * Get transactions matching the specified search criteria.
     *
     * @param searchCriteria {@link TransactionSearchCriteria} which can be used to restrict results
     * of the search and apply paging and sorting. Never {@code null}.
     * @return Page with found transactions.
     */
    Page<Transaction> getTransactions(TransactionSearchCriteria searchCriteria);

    /**
     * Get transaction with the given ID.
     *
     * @param id Transaction ID.
     * @return Found transaction. Never {@code null}.
     * @throws NotFoundException if transaction with the given ID was not found.
     */
    Transaction getTransaction(long id) throws NotFoundException;

    /**
     * Create xls file with all transactions matching the specified criteria.
     *
     * @param searchCriteria {@link TransactionSearchCriteria} which can be used to restrict results
     * @param outFile File for store XLS
     */
    void getTransactionXls(TransactionSearchCriteria searchCriteria, File outFile);
}
