package cz.wincor.ovv.viewer.service.impl;

import java.util.regex.Pattern;

import cz.wincor.ovv.viewer.security.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.querydsl.core.BooleanBuilder;

import cz.wincor.ovv.viewer.dto.DtoPage;
import cz.wincor.ovv.viewer.dto.PasswordChangeRequest;
import cz.wincor.ovv.viewer.dto.UserDto;
import cz.wincor.ovv.viewer.exception.NotFoundException;
import cz.wincor.ovv.viewer.exception.ValidationException;
import cz.wincor.ovv.viewer.model.UserRole;
import cz.wincor.ovv.viewer.model.entity.QUser;
import cz.wincor.ovv.viewer.model.entity.Store;
import cz.wincor.ovv.viewer.model.entity.User;
import cz.wincor.ovv.viewer.model.search.UserSearchCriteria;
import cz.wincor.ovv.viewer.repository.StoreRepository;
import cz.wincor.ovv.viewer.repository.UserRepository;
import cz.wincor.ovv.viewer.service.UserService;
import cz.wincor.ovv.viewer.utils.MapperUtils;
import cz.wincor.ovv.viewer.validation.ErrorCode;
import cz.wincor.ovv.viewer.validation.ValidationHelper;

/**
 * Default implementation of {@link UserService}.
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = ValidationException.class)
public class UserServiceImpl extends BaseServiceImpl implements UserService {

    private static final Logger LOG = LoggerFactory.getLogger(UserServiceImpl.class);

    private static final Pattern PASSWORD_PATTERN = Pattern.compile("^.{8,}$");

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private StoreRepository storeRepository;
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public UserDto getUserById(long userId) throws NotFoundException {
        return getCheckedUser(userId).mapToDto();
    }

    @Override
    public DtoPage<UserDto> getUsers(UserSearchCriteria searchCriteria) {
        LOG.info("Getting users : " + searchCriteria.toString());
        BooleanBuilder predicate = new BooleanBuilder();

        if (StringUtils.isNotBlank(searchCriteria.getUsername())) {
            predicate.and(QUser.user.username.containsIgnoreCase(searchCriteria.getUsername()));
        }

        if (StringUtils.isNotBlank(searchCriteria.getName())) {
            predicate.and(QUser.user.firstName.containsIgnoreCase(searchCriteria.getName())
                    .or(QUser.user.lastName.containsIgnoreCase(searchCriteria.getName())));
        }

        if (searchCriteria.getRole() != null) {
            predicate.and(QUser.user.role.eq(searchCriteria.getRole()));
        }

        if (searchCriteria.getActive() != null) {
            predicate.and(QUser.user.active.eq(searchCriteria.getActive()));
        }

        User loggedUser = SecurityUtils.getLoggedUser();
        // restrict stores for non-admin users and roles of users visible
        if (loggedUser.getRole() != UserRole.ADMIN) {
            BooleanBuilder storesCondition = new BooleanBuilder();
            for (Store store : loggedUser.getStores()) {
                storesCondition.or(QUser.user.stores.contains(store));
            }
            predicate.and(storesCondition);
            predicate.and(QUser.user.role.notIn(UserRole.ADMIN, UserRole.STORE_MANAGER));
        }

        return MapperUtils.mapPage(findAll(userRepository, predicate, searchCriteria));
    }

    @Override
    public UserDto createUser(UserDto userDto) throws ValidationException {
        LOG.info("Creating user : " + userDto.toString());
        // Validate user data
        ValidationHelper validation = new ValidationHelper();
        validateUsername(validation, userDto.getUsername());
        validatePassword(validation, userDto.getPassword());
        validation.notNull(userDto.getRole(), "role", "Missing user role.");
        if(!userDto.getRole().equals(UserRole.ADMIN)) {
            validation.notNull(userDto.getCountryCode(), "country", "Country is mandatory for non-admin user.");
        }
        validation.checkErrors();

        // Map all data to a new User entity
        User user = new User();
        user.setUsername(userDto.getUsername());
        user.setFirstName(userDto.getFirstName());
        user.setLastName(userDto.getLastName());
        user.setEmail(userDto.getEmail());
        user.setRole(userDto.getRole());
        user.setPassword(passwordEncoder.encode(userDto.getPassword()));
        user.setAccountNonExpired(false);
        user.setActive(userDto.isActive());
        user.setStores(userDto.getStores());
        user.setCountryCode(userDto.getCountryCode());
        // Save the new User entity
        UserDto userDTO = userRepository.save(user).mapToDto();
        publisher.publishEvent(new UserCreatedEvent(userDTO.getUsername(), "User created"));
        return userDTO;
    }

    @Override
    public UserDto updateUser(UserDto userDto) throws NotFoundException, ValidationException {
        LOG.info("Updating user : " + userDto.toString());
        // check whether the caller has filled in a new password
        boolean updatePassword = StringUtils.isNotBlank(userDto.getPassword());

        // Validate user data
        ValidationHelper validation = new ValidationHelper();
        if (updatePassword) {
            validatePassword(validation, userDto.getPassword());
        }
        validation.notNull(userDto.getRole(), "role", "Missing user role.");
        if(!userDto.getRole().equals(UserRole.ADMIN)) {
            validation.notNull(userDto.getCountryCode(), "country", "Country is mandatory for non-admin user.");
        }
        validation.checkErrors();

        User user = getCheckedUser(userDto.getId());
        user.setFirstName(userDto.getFirstName());
        user.setLastName(userDto.getLastName());
        if (updatePassword) {
            user.setPassword(passwordEncoder.encode(userDto.getPassword()));
            user.setAccountNonExpired(false);
            publisher.publishEvent(new UserPasswordChangedEvent(userDto.getUsername(), "Password changed"));
        }
        if(user.getRole()!= userDto.getRole()){
            publisher.publishEvent(new UserRoleUpdated(userDto.getUsername(), "User role updated", user.getRole(), userDto.getRole()));
        }
        user.setRole(userDto.getRole());
        user.setEmail(userDto.getEmail());
        if(userDto.isActive() != user.isActive()){
            publisher.publishEvent(new UserStatusChangedEvent(userDto.getUsername(), "User status updated", userDto.isActive()));
        }
        user.setActive(userDto.isActive());
        user.setStores(userDto.getStores());
        user.setCountryCode(userDto.getCountryCode());
        // Save the new User entity
        return userRepository.save(user).mapToDto();
    }

    /**
     * Get {@link User} by its ID and verify that it exists.
     *
     * @param userId
     *            User ID.
     * @return Existing {@link User}. Never {@code null}.
     * @throws NotFoundException
     *             if user was not found.
     */
    private User getCheckedUser(long userId) throws NotFoundException {
        return userRepository.findById(userId).orElseThrow(()-> new NotFoundException("User", userId));
    }

    /**
     * Validate the the given username is not empty (blank string) and that it is
     * not used by an already existing user.
     *
     * @param validation
     *            Validation helper to process and collect validation errors.
     * @param username
     *            Username to be validated.
     */
    private void validateUsername(ValidationHelper validation, String username) {
        if (validation.notBlank(username, "username", "Username is missing.")) {
            User existingUser = userRepository.findByUsername(username);
            validation.isTrue(existingUser == null, "username", "username.taken", "Username is already taken.");
        }
    }

    /**
     * Validate new password. Passwords must meet these requirements for proper
     * password strength:
     * <ul>
     * <li>is at least 8 characters long,</li>
     * <li>contains at least 1 digit,</li>
     * <li>contains at least 1 lower case letter,</li>
     * <li>contains at least 1 upper case letter.</li>
     * </ul>
     *
     * @param validation
     *            Validation helper to process and collect validation errors.
     * @param password
     *            New password to be validated.
     */
    private void validatePassword(ValidationHelper validation, String password) {
        if (validation.notBlank(password, "password", "Password is missing")) {
            // validate password strength
            validation.isTrue(PASSWORD_PATTERN.matcher(password).matches(), "password", "password.tooWeak",
                    "Password is not strong enough.");
        }
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userRepository.findByUsername(username);
        if (user == null) {
            throw new UsernameNotFoundException("User '" + username + "' was not found.");
        }
        return user;
    }

    @Override
    public void changePassword(long userId, PasswordChangeRequest passwordChangeRequest)
            throws ValidationException, NotFoundException {
        User user = getCheckedUser(userId);
        // Validate user data
        ValidationHelper validation = new ValidationHelper();

        if (!SecurityUtils.hasRole(UserRole.ADMIN)
                && !passwordEncoder.matches(passwordChangeRequest.getOldPassword(), user.getPassword())) {
            validation.error("oldPassword", ErrorCode.OBJECT_INVALID, "The old password does not match.");
        }
        validatePassword(validation, passwordChangeRequest.getNewPassword());
        validation.checkErrors();

        user.setPassword(passwordEncoder.encode(passwordChangeRequest.getNewPassword()));
        userRepository.save(user);
        publisher.publishEvent(new UserPasswordChangedEvent(user.getUsername(), "Password changed"));

    }
    @Override
    public void expiredPasswordChange(cz.wincor.ovv.viewer.controller.api.PasswordChangeRequest passwordChangeRequest)
            throws ValidationException, NotFoundException {
        User user = userRepository.findByUsername(passwordChangeRequest.getUserName());
        // Validate user data
        ValidationHelper validation = new ValidationHelper();

        if (!passwordEncoder.matches(passwordChangeRequest.getOldPassword(), user.getPassword())) {
            validation.error("oldPassword", ErrorCode.OBJECT_INVALID, "The old password does not match.");
        }
        validatePassword(validation, passwordChangeRequest.getNewPassword());
        validation.checkErrors();

        user.setPassword(passwordEncoder.encode(passwordChangeRequest.getNewPassword()));
        user.setAccountNonExpired(true);
        publisher.publishEvent(new UserPasswordChangedEvent(user.getUsername(), "Expired Password changed"));
        FakedAuthenticationExecutor.execute(user, new FakedAuthenticationExecutor.ExecutionCallback<Void>() {
            @Override
            public Void execute() {
                userRepository.saveAndFlush(user);
                return null;
            }
        });
    }


    @Override
    public UserDto getLoggedUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserDetails principal = (UserDetails) authentication.getPrincipal();
        return null;
    }

}
