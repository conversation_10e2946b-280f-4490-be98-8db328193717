package cz.wincor.ovv.viewer.service;


import cz.wincor.ovv.viewer.dto.DtoPage;
import cz.wincor.ovv.viewer.dto.ReportBasicDTO;
import cz.wincor.ovv.viewer.dto.ReportDTO;
import cz.wincor.ovv.viewer.dto.ReportSubmitRequest;
import cz.wincor.ovv.viewer.exception.NotFoundException;
import cz.wincor.ovv.viewer.exception.ValidationException;
import cz.wincor.ovv.viewer.model.search.ReportSearchCriteria;

/**
 * Service interface for reports.
 *
 * <AUTHOR>
 */
public interface ReportService {

    ReportDTO submitReport(ReportSubmitRequest request) throws ValidationException;

    DtoPage<ReportBasicDTO> getReports(ReportSearchCriteria searchCriteria);

    ReportDTO getReportById(long reportId) throws NotFoundException;

}
