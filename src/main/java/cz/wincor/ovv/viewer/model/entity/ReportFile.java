package cz.wincor.ovv.viewer.model.entity;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.time.LocalDateTime;

/**
 * Report File entity.
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "report_file")
public class ReportFile {

    @Id
    private Long id;

    @Column(name = "country_code")
    private String countryCode;

    @Column(name = "created", length = 2, unique = true)
    private LocalDateTime created;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "filename_date", unique = true)
    private String fileNameDate;

    @Column(name = "file_path", unique = true)
    private String filePath;

    @Column(name = "items_count")
    private Integer itemsCount;

    @Column(name = "ovv_issuer", length = 2, unique = true)
    private String ovvIssuer;

    @Column(name = "reconciliated")
    private LocalDateTime reconciliated;

    @Column(name = "uuid", unique = true)
    private Long uuid;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public LocalDateTime getCreated() {
        return created;
    }

    public void setCreated(LocalDateTime created) {
        this.created = created;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileNameDate() {
        return fileNameDate;
    }

    public void setFileNameDate(String fileNameDate) {
        this.fileNameDate = fileNameDate;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Integer getItemsCount() {
        return itemsCount;
    }

    public void setItemsCount(Integer itemsCount) {
        this.itemsCount = itemsCount;
    }

    public String getOvvIssuer() {
        return ovvIssuer;
    }

    public void setOvvIssuer(String ovvIssuer) {
        this.ovvIssuer = ovvIssuer;
    }

    public LocalDateTime getReconciliated() {
        return reconciliated;
    }

    public void setReconciliated(LocalDateTime reconciliated) {
        this.reconciliated = reconciliated;
    }

    public Long getUuid() {
        return uuid;
    }

    public void setUuid(Long uuid) {
        this.uuid = uuid;
    }
}
