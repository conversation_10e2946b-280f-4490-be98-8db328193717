package cz.wincor.ovv.viewer.model;

/**
 * Enumeration of report statuses.
 *
 * <AUTHOR>
 */
public enum ReportStatus {
    /**
     * Report submitted and waiting for processing in the report queue.
     */
    QUEUED,
    /**
     * Report in progress, i.e. being processed.
     */
    IN_PROGRESS,
    /**
     * Report successfully processed and created.
     */
    OK,
    /**
     * Report processing failed due to an error.
     */
    FAILED,
    /**
     * Report processing failed due to large amount of data.
     */
    LARGE_EXPORT
}
