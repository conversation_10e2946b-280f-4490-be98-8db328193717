package cz.wincor.ovv.viewer.model.entity;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

/**
 * Report batch entity.
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "report_batch")
public class ReportBatch {

    @Id
    private Long id;

    @Column(name = "ovv_transaction_request_id")
    private Long transactionRequestId;

    @Column(name = "result_code")
    private Integer resultCode;

    @Column(name = "voucher_number")
    private String voucherNumber;

    @Column(name = "result_description")
    private String resultDescription;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "report_file_id")
    private ReportFile reportFile;

    @ManyToOne(fetch = FetchType.EAGER)
    @NotFound(action=NotFoundAction.IGNORE)
    @JoinColumn(name = "store_id")
    private Store store;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTransactionRequestId() {
        return transactionRequestId;
    }

    public void setTransactionRequestId(Long transactionRequestId) {
        this.transactionRequestId = transactionRequestId;
    }

    public Integer getResultCode() {
        return resultCode;
    }

    public void setResultCode(Integer resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultDescription() {
        return resultDescription;
    }

    public void setResultDescription(String resultDescription) {
        this.resultDescription = resultDescription;
    }

    public ReportFile getReportFile() {
        return reportFile;
    }

    public void setReportFile(ReportFile reportFile) {
        this.reportFile = reportFile;
    }

    public Store getStore() {
        return store;
    }

    public void setStore(Store store) {
        this.store = store;
    }

    public String getVoucherNumber() {
        return voucherNumber;
    }

    public void setVoucherNumber(String voucherNumber) {
        this.voucherNumber = voucherNumber;
    }
}
