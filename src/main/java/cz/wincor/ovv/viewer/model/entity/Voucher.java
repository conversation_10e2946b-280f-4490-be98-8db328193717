package cz.wincor.ovv.viewer.model.entity;

import cz.wincor.ovv.viewer.model.CountryCode;
import cz.wincor.ovv.viewer.model.OvvVoucherState;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "VOUCHER")
public class Voucher {

    @Id
    private Long id;

    @NotFound(action = NotFoundAction.IGNORE)
    @OneToOne
    @JoinColumn(name = "related_transaction_request_id")
    private Transaction relatedTransactionRequest;

    @Column(name = "voucher_number", nullable = false)
    private String voucherNumber;

    @Column(name = "voucher_state", length = 32, nullable = false)
    @Enumerated(EnumType.STRING)
    private OvvVoucherState voucherState;

    @Column(name = "server_local_date_time", nullable = false)
    private LocalDateTime serverLocalDateTime;

    @Column(name = "country_code", length = 16, nullable = false)
    @Enumerated(EnumType.STRING)
    private CountryCode countryCode;

    @Column(name = "expiration_date_time")
    private LocalDateTime expirationDateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Transaction getRelatedTransactionRequest() {
        return relatedTransactionRequest;
    }

    public void setRelatedTransactionRequest(Transaction relatedTransactionRequest) {
        this.relatedTransactionRequest = relatedTransactionRequest;
    }

    public String getVoucherNumber() {
        return voucherNumber;
    }

    public void setVoucherNumber(String voucherNumber) {
        this.voucherNumber = voucherNumber;
    }

    public OvvVoucherState getVoucherState() {
        return voucherState;
    }

    public void setVoucherState(OvvVoucherState voucherState) {
        this.voucherState = voucherState;
    }

    public LocalDateTime getServerLocalDateTime() {
        return serverLocalDateTime;
    }

    public void setServerLocalDateTime(LocalDateTime serverLocalDateTime) {
        this.serverLocalDateTime = serverLocalDateTime;
    }

    public CountryCode getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(CountryCode countryCode) {
        this.countryCode = countryCode;
    }

    public LocalDateTime getExpirationDateTime() {
        return expirationDateTime;
    }

    public void setExpirationDateTime(LocalDateTime expirationDateTime) {
        this.expirationDateTime = expirationDateTime;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", id)
                .append("relatedTransactionRequest", relatedTransactionRequest)
                .append("voucherNumber", voucherNumber)
                .append("voucherState", voucherState)
                .append("serverLocalDateTime", serverLocalDateTime)
                .append("countryCode", countryCode)
                .append("expirationDateTime", expirationDateTime)
                .toString();
    }
}
