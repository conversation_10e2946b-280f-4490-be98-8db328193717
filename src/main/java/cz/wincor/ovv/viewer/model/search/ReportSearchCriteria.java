package cz.wincor.ovv.viewer.model.search;

import java.time.LocalDate;

import org.apache.commons.lang3.builder.ToStringBuilder;

import cz.wincor.ovv.viewer.model.ReportStatus;
import cz.wincor.ovv.viewer.model.ReportType;

/**
 * Criteria for searching Report entities.
 *
 * <AUTHOR>
 */
public class ReportSearchCriteria extends BaseSearchCriteria {

    private ReportType type;
    private ReportStatus status;
    private LocalDate dateFrom;
    private LocalDate dateTo;


    //~ Plain getters & setters

    public ReportType getType() {
        return type;
    }
    public void setType(ReportType type) {
        this.type = type;
    }

    public ReportStatus getStatus() {
        return status;
    }
    public void setStatus(ReportStatus status) {
        this.status = status;
    }

    public LocalDate getDateFrom() {
        return dateFrom;
    }
    public void setDateFrom(LocalDate dateFrom) {
        this.dateFrom = dateFrom;
    }

    public LocalDate getDateTo() {
        return dateTo;
    }
    public void setDateTo(LocalDate dateTo) {
        this.dateTo = dateTo;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("type", type)
                .append("status", status)
                .append("dateFrom", dateFrom)
                .append("dateTo", dateTo)
                .append("page", getPage())
                .append("size", getSize())
                .append("sortBy", getSortBy())
                .append("direction", getDirection())
                .append("storeIds", getStoreIds())
                .toString();
    }
}
