package cz.wincor.ovv.viewer.model.search;

import org.apache.commons.lang3.builder.ToStringBuilder;

import cz.wincor.ovv.viewer.model.UserRole;

/**
 * Criteria for searching User entities.
 *
 * <AUTHOR>
 */
public class UserSearchCriteria extends BaseSearchCriteria {

    private String username;
    private String name;
    private UserRole role;
    private Boolean active;


    //~ Plain getters & setters

    public String getUsername() {
        return username;
    }
    public void setUsername(String username) {
        this.username = username;
    }

    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }

    public UserRole getRole() {
        return role;
    }
    public void setRole(UserRole role) {
        this.role = role;
    }

    public Boolean getActive() {
        return active;
    }
    public void setActive(Boolean active) {
        this.active = active;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("username", username)
                .append("name", name)
                .append("role", role)
                .append("active", active)
                .append("page", getPage())
                .append("size", getSize())
                .append("sortBy", getSortBy())
                .append("direction", getDirection())
                .append("storeIds", getStoreIds())
                .toString();
    }
}
