 package cz.wincor.ovv.viewer.model.entity;

 import jakarta.persistence.Column;
 import jakarta.persistence.Entity;
 import jakarta.persistence.Id;
 import jakarta.persistence.Table;

 import java.time.LocalDateTime;

/**
 * Transaction entity.
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "repl_ovv_transaction")
public class Transaction {

    @Id
    private long id;

    @Column(name = "device_id", nullable = false, length = 255)
    private String deviceId;

    @Column(name = "transaction_type", nullable = false, length = 32)
    private String transactionType;

    @Column(name = "voucher_number", length = 255)
    private String voucherNumber;

    @Column(name = "stan")
    private int stan;

    @Column(name = "amount")
    private long amount;

    @Column(name = "repeated", nullable = false)
    private boolean repeated;

    @Column(name = "module", nullable = false, length = 255)
    private String module;

    @Column(name = "device_local_date_time", nullable = false)
    private LocalDateTime deviceDateTime;

    @Column(name = "server_local_date_time", nullable = false)
    private LocalDateTime serverDateTime;

    @Column(name = "server_utc_date_time", nullable = false)
    private LocalDateTime serverDateTimeUtc;

    @Column(name = "client_ip_address", length = 255)
    private String clientIp;

    @Column(name = "country_code", nullable = false, length = 16)
    private String countryCode;

    @Column(name = "offline_mode", nullable = false)
    private boolean offlineMode;

    @Column(name = "ovv_issuer", length = 255)
    private String ovvIssuer;

    @Column(name = "partner_id", length = 255)
    private String partnerId;

    @Column(name = "payment_place", length = 255)
    private String paymentPlace;

    @Column(name = "pos_country_code")
    private int posCountryCode;

    @Column(name = "pos_currency_code")
    private int posCurrencyCode;

    @Column(name = "result_code", nullable = false, length = 32)
    private String resultCode;

    @Column(name = "validation_host", length = 255)
    private String validationHost;

    @Column(name = "previous_request_id")
    private long previousRequestId;

    @Column(name = "error_description", length = 255)
    private String errorDescription;

    @Column(name = "expiration_date_time", nullable = false)
    private LocalDateTime expirationDate;

    @Column(name = "manual_redemption")
    private Boolean manualRedemption;

    @Column(name = "training_mode")
    private Boolean trainingMode;

    @Column(name = "category", length = 16)
    private String category;

    //~ Plain getters & setters

    public long getId() {
        return id;
    }
    public void setId(long id) {
        this.id = id;
    }

    public String getDeviceId() {
        return deviceId;
    }
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getTransactionType() {
        return transactionType;
    }
    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getVoucherNumber() {
        return voucherNumber;
    }
    public void setVoucherNumber(String voucherNumber) {
        this.voucherNumber = voucherNumber;
    }

    public int getStan() {
        return stan;
    }
    public void setStan(int stan) {
        this.stan = stan;
    }

    public long getAmount() {
        return amount;
    }
    public void setAmount(long amount) {
        this.amount = amount;
    }

    public boolean isRepeated() {
        return repeated;
    }
    public void setRepeated(boolean repeated) {
        this.repeated = repeated;
    }

    public String getModule() {
        return module;
    }
    public void setModule(String module) {
        this.module = module;
    }

    public LocalDateTime getDeviceDateTime() {
        return deviceDateTime;
    }
    public void setDeviceDateTime(LocalDateTime deviceDateTime) {
        this.deviceDateTime = deviceDateTime;
    }

    public LocalDateTime getServerDateTime() {
        return serverDateTime;
    }
    public void setServerDateTime(LocalDateTime serverDateTime) {
        this.serverDateTime = serverDateTime;
    }

    public LocalDateTime getServerDateTimeUtc() {
        return serverDateTimeUtc;
    }
    public void setServerDateTimeUtc(LocalDateTime serverDateTimeUtc) {
        this.serverDateTimeUtc = serverDateTimeUtc;
    }

    public String getClientIp() {
        return clientIp;
    }
    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getCountryCode() {
        return countryCode;
    }
    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public boolean isOfflineMode() {
        return offlineMode;
    }
    public void setOfflineMode(boolean offlineMode) {
        this.offlineMode = offlineMode;
    }

    public String getOvvIssuer() {
        return ovvIssuer;
    }
    public void setOvvIssuer(String ovvIssuer) {
        this.ovvIssuer = ovvIssuer;
    }

    public String getPartnerId() {
        return partnerId;
    }
    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getPaymentPlace() {
        return paymentPlace;
    }
    public void setPaymentPlace(String paymentPlace) {
        this.paymentPlace = paymentPlace;
    }

    public int getPosCountryCode() {
        return posCountryCode;
    }
    public void setPosCountryCode(int posCountryCode) {
        this.posCountryCode = posCountryCode;
    }

    public int getPosCurrencyCode() {
        return posCurrencyCode;
    }
    public void setPosCurrencyCode(int posCurrencyCode) {
        this.posCurrencyCode = posCurrencyCode;
    }

    public String getResultCode() {
        return resultCode;
    }
    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getValidationHost() {
        return validationHost;
    }
    public void setValidationHost(String validationHost) {
        this.validationHost = validationHost;
    }

    public long getPreviousRequestId() {
        return previousRequestId;
    }
    public void setPreviousRequestId(long previousRequestId) {
        this.previousRequestId = previousRequestId;
    }

    public String getErrorDescription() {
        return errorDescription;
    }
    public void setErrorDescription(String errorDescription) {
        this.errorDescription = errorDescription;
    }

    public LocalDateTime getExpirationDate() {
        return expirationDate;
    }
    public void setExpirationDate(LocalDateTime expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Boolean getManualRedemption() {
        return manualRedemption;
    }

    public void setManualRedemption(Boolean manualRedemption) {
        this.manualRedemption = manualRedemption;
    }


    public Boolean getTrainingMode() {
        return trainingMode;
    }

    public void setTrainingMode(Boolean trainingMode) {
        this.trainingMode = trainingMode;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
}
