package cz.wincor.ovv.viewer.model.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.time.LocalDateTime;


/**
 * Cash register - store mapping.
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "ovv_cash_register")
public class CashRegister {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "number")
    private String number;

    @Column(name = "site_code", unique = true)
    private String siteCode;

    @Column(name = "active", nullable = false)
    private boolean active = true;

    @Column(name = "import_date")
    private LocalDateTime importDateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public LocalDateTime getImportDateTime() {
        return importDateTime;
    }

    public void setImportDateTime(LocalDateTime importDateTime) {
        this.importDateTime = importDateTime;
    }

}
