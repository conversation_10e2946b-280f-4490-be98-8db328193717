package cz.wincor.ovv.viewer.pojo;

import com.opencsv.bean.CsvBindByPosition;

/**
 * Entries in cash registr import files.
 *
 * <AUTHOR>
 *
 */
public class CashRegisterImportEntry {
    @CsvBindByPosition(position = 0)
    private String siteCode;
    @CsvBindByPosition(position = 1)
    private String cashRegisterNumber;

    public CashRegisterImportEntry() {
    }

    public CashRegisterImportEntry(String siteCode, String cashRegisterNumber) {
        super();
        this.siteCode = siteCode;
        this.cashRegisterNumber = cashRegisterNumber;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getCashRegisterNumber() {
        return cashRegisterNumber;
    }

    public void setCashRegisterNumber(String cashRegisterNumber) {
        this.cashRegisterNumber = cashRegisterNumber;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("CashRegisterImportEntry [siteCode=");
        builder.append(siteCode);
        builder.append(", cashRegisterNumber=");
        builder.append(cashRegisterNumber);
        builder.append("]");
        return builder.toString();
    }

}
