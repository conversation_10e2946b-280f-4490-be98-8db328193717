package cz.wincor.ovv.viewer.pojo;

public class HTTPMessage<E> extends GeneralMessage<E> {

	private int httpStatus;
	private String requestUrl;

	public HTTPMessage(String clientId, E data) {
		super(clientId, data);
	}

	/**
	 * @return the responseCode
	 */
	public int getHttpStatus() {
		return httpStatus;
	}

	/**
	 * @return the requestUrl
	 */
	public String getRequestUrl() {
		return requestUrl;
	}

	/**
	 * @param responseCode the responseCode to set
	 */
	public void setHttpStatus(int responseCode) {
		this.httpStatus = responseCode;
	}

	/**
	 * @param requestUrl the requestUrl to set
	 */
	public void setRequestUrl(String requestUrl) {
		this.requestUrl = requestUrl;
	}






}
