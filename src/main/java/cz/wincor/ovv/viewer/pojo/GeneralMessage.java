package cz.wincor.ovv.viewer.pojo;

public class GeneralMessage<E> implements Message<E> {

	private String clientId;
	private E data;

	/**
	 * Create Request with data param only
	 * @param data
	 */
	public GeneralMessage(String clientId, E data) {
		this.data = data;
		this.clientId = clientId;
	}


	@Override
	public String getClientId() {
		return clientId;
	}


	@Override
	public E getData() {
		return data;
	}



}
