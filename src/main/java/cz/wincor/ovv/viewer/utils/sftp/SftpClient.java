package cz.wincor.ovv.viewer.utils.sftp;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Properties;
import java.util.Vector;

import org.apache.commons.lang3.StringUtils;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;

/**
 * Convenience class for establishing a connection via SFTP, performing actions
 * via that connection and for cleanly closing such a connection.
 *
 * <AUTHOR>
 *
 */
public class SftpClient implements AutoCloseable {

    /** JSCh SFTP channel */
    private ChannelSftp sftp;

    /** JSCh session */
    private Session session;

    /**
     * @param sftpServer
     *            server host name or IP address
     * @param sftpUser
     *            user login name
     * @param usePrivateKey
     *            "true" to use private key authentication, "false" to use password
     *            authentication
     * @param sftpPassword
     *            password to use for authentication (<code>null</code> if
     *            <code>usePrivateKey</code> is true)
     * @param sftpPrivatekey
     *            path to private key file (<code>null</code> if
     *            <code>usePrivateKey</code> is false)
     * @param sftpPrivateKeyPassphrase
     *            TODO
     */
    public SftpClient(String sftpServer, int port, String sftpUser, String sftpPassword) throws JSchException {
        this(sftpServer, sftpUser, false, sftpPassword, null, null, port);
    }

    public SftpClient(String sftpServer, int port, String sftpUser, String sftpPrivatekey,
            String sftpPrivateKeyPassphrase) throws JSchException {
        this(sftpServer, sftpUser, true, null, sftpPrivatekey, sftpPrivateKeyPassphrase, port);
    }

    public SftpClient(String sftpServer, String sftpUser, boolean usePrivateKey, String sftpPassword,
            String sftpPrivateKey, String sftpPrivateKeyPassphrase, int port) throws JSchException {

        JSch jSch = new JSch();

        if (usePrivateKey) {
            if (StringUtils.isNotBlank(sftpPrivateKeyPassphrase)) {
                jSch.addIdentity(new File(sftpPrivateKey).getAbsolutePath(), sftpPrivateKeyPassphrase.trim());
            } else {
                jSch.addIdentity(new File(sftpPrivateKey).getAbsolutePath());
            }
        }

        session = jSch.getSession(sftpUser, sftpServer, port);
        Properties SSH_PROPERTIES = new Properties();
        SSH_PROPERTIES.put("StrictHostKeyChecking", "No");
        session.setConfig(SSH_PROPERTIES);

        if (!usePrivateKey) {
            session.setPassword(sftpPassword);
        }

        session.connect();

        Channel channel = session.openChannel("sftp");
        sftp = (ChannelSftp) channel;
        sftp.connect();
    }

    /**
     * Changes the current directory.
     *
     * @param directory
     *            path to the directory to be switched to
     */
    public void cd(String directory) throws SftpException {
        sftp.cd(directory);
    }

    /**
     * Lists the given directory.
     *
     * @param directory
     *            path to the directory to be listed
     */
    @SuppressWarnings("unchecked")
    public Vector<LsEntry> ls(String directory) throws SftpException {
        return sftp.ls(directory);
    }

    /**
     * Deletes the given file.
     *
     * @param fileName
     *            path to the file, absolute or relative to the current directory
     * @throws SftpException
     */
    public void rm(String fileName) throws SftpException {
        sftp.rm(fileName);
    }

    /**
     * Closes the connection gracefully.
     */
    public void close() {
        sftp.disconnect();
        session.disconnect();
    }

    public void put(InputStream source, String destinationFile) throws SftpException {
        sftp.put(source, destinationFile);
    }

    public OutputStream put(String destinationFile, int mode) throws SftpException {
        return sftp.put(destinationFile, mode);
    }

    public InputStream get(String sourceFile) throws SftpException {
        return sftp.get(sourceFile);
    }

    public void get(String sourceFile, String destinationFile) throws SftpException {
        sftp.get(sourceFile, destinationFile);
    }

    public void rename(String oldpath, String newpath) throws SftpException {
        sftp.rename(oldpath, newpath);
    }

    public void get(String file, OutputStream stream) throws SftpException {
        sftp.get(file, stream);
    }
}
