package cz.wincor.ovv.viewer.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility class for HTTP communication
 *
 * <AUTHOR>
 *
 */
public class HTTPUtil {

	private static final Pattern CHARSET_PATTERN = Pattern
			.compile("(?i)\\bcharset=\\s*\"?([^\\s;\"]*)");

	/**
	 * Parse out a charset from a content type header.
	 *
	 * @param contentType
	 *            e.g. "text/html; charset=UTF-8"
	 * @return "UTF-8", or null if not found. Charset is trimmed and uppercased.
	 */
	public static String getCharsetFromContentType(String contentType) {
		if (contentType == null)
			return null;

		final Matcher m = CHARSET_PATTERN.matcher(contentType);
		if (m.find()) {
			return m.group(1).trim().toUpperCase();
		}
		return null;
	}

	//Dummy private constructor
	private HTTPUtil() {
	}

}
