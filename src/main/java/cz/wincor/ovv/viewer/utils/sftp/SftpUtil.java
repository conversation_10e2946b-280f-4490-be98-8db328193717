package cz.wincor.ovv.viewer.utils.sftp;

import java.io.File;
import java.util.Vector;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.jcraft.jsch.ChannelSftp.LsEntry;
import com.jcraft.jsch.SftpException;

import cz.wincor.ovv.viewer.utils.Util;

public class SftpUtil {
    private static final Logger LOG = LoggerFactory.getLogger(SftpUtil.class);

    /**
     * Downloads all files from remote folder to local folder.
     *
     * @param sftpFolder
     * @param downloadFolder
     * @param sftpClient
     * @throws SftpException
     */
    public static void downloadFilesFromFolder(String sftpFolder, File downloadFolder, SftpClient sftpClient)
            throws SftpException {
        LOG.info("Downloading from sftp folder: " + sftpFolder);
        Vector<LsEntry> fileEntries = sftpClient.ls(sftpFolder);
        for (LsEntry lsEntry : fileEntries) {
            // some sftp servers return also . and ..
            if (!lsEntry.getAttrs().isDir()) {
                String source = sftpFolder + "/" + lsEntry.getFilename();
                String target = downloadFolder.getPath() + File.separator + lsEntry.getFilename();
                LOG.info("Downloading " + source + " to " + target);
                sftpClient.get(source, target);
            }
        }
    }

    /**
     * Move files from one remote folder to another archive folder.
     *
     * @param sourceSftpFolder
     * @param archiveSftpFolder
     * @param sftpClient
     * @throws SftpException
     */
    public static void remotellyArchiveFiles(String sourceSftpFolder, String archiveSftpFolder, SftpClient sftpClient)
            throws SftpException {
        Vector<LsEntry> fileEntries = sftpClient.ls(sourceSftpFolder);
        for (LsEntry lsEntry : fileEntries) {
            // some sftp servers return also . and ..
            if (!lsEntry.getAttrs().isDir()) {
                String archiveFileName = Util.addTimestamp(lsEntry.getFilename());
                String source = sourceSftpFolder + "/" + lsEntry.getFilename();
                String target = archiveSftpFolder + "/" + archiveFileName;
                LOG.info("Archiving " + source + " to " + target);
                sftpClient.rename(source, target);
            }
        }
    }

}
