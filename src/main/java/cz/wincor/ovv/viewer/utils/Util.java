package cz.wincor.ovv.viewer.utils;

import java.io.File;
import java.io.IOException;
import java.util.Iterator;
import java.util.Spliterator;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import com.google.common.io.Files;

public class Util {

    /**
     * Converts iterator to spliterator.
     * 
     * @param beanConverter
     * @return
     */
    public static <T> Spliterator<T> getSpliterator(Iterator<T> iterator) {
        Iterator<T> sourceIterator = iterator;
    
        Iterable<T> iterable = () -> sourceIterator;
        Stream<T> importEntriesStream = StreamSupport.stream(iterable.spliterator(), false);
    
        Spliterator<T> split = importEntriesStream.spliterator();
        return split;
    }

    /**
     * Adds timestamp inside filename.
     * @param fileName
     * @return
     */
    public static String addTimestamp(String fileName) {
        String[] splittedName = fileName.split("\\.");
        String archiveFileName = splittedName[0] + "-" + System.currentTimeMillis() + "." + splittedName[1];
        return archiveFileName;
    }

    /**
     * Moves files to local archive folder.
     * 
     * @param downloadFolder
     * @param archiveFolder
     * @throws IOException
     */
    public static void archiveLocally(File downloadFolder, String archiveFolder) throws IOException {
        File[] files = downloadFolder.listFiles();
        for (int i = 0; i < files.length; i++) {
            String archiveFileName = addTimestamp(files[i].getName());
            File archiveFile = new File(archiveFolder + File.separator + archiveFileName);
            Files.move(files[i], archiveFile);
        }
    }

}
