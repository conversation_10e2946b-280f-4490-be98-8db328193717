package cz.wincor.ovv.viewer.utils;

import org.slf4j.Logger;

/**
 * Gateway Logger Util class
 * <AUTHOR> pavel.sklenar
 *
 */
public class GWLogger {

	private static final String LINE_SEP = System.getProperty("line.separator");

	/**
	 * Naformatuje data pro aplikacni log - hex/ascii.
	 * <p>
	 * POZOR NA LOGOVANI CITLIVYCH INFORMACI!!
	 */
	private static String dump(byte[] data) {

		int i = 0, j = 0; // loop counters
		int offset = 0; // memmory address printed on the left

		if (data == null) {
			return "null";
		}

		if (data.length == 0) {
			return "";
		}

		final StringBuilder buf = new StringBuilder();

		// Loop through every input byte
		String hex = "";
		String ascii = "";

		for (i = 0, offset = 0; i < data.length; i++, offset++) {
			// Print the line numbers at the beginning of the line
			if ((i % 16) == 0) {
				if (i != 0) {
					buf.append(hex);
					buf.append(ascii + LINE_SEP);
				}
				ascii = "";
				hex = String.format("%#06x ", offset);
			}

			hex = hex.concat(String.format("%#04x ", data[i]));
			if (data[i] > 31 && data[i] < 127) {
				ascii = ascii.concat(String.valueOf((char) data[i]));
			} else {
				ascii = ascii.concat(".");
			}
		}

		// Handle the ascii for the final line, which may not be completely filled.
		if (i % 16 > 0) {
			for (j = 0; j < 16 - (i % 16); j++) {
				hex = hex.concat("     ");
			}

			buf.append(hex);
			buf.append(ascii);
		}

		return buf.toString();
    }

    /**
     * Zaloguje obecna data - hex/ascii format.
     * <p>
     * POZOR NA LOGOVANI CITLIVYCH INFORMACI!!
     */
	public static void logHexDebug(String label, byte[] data, Logger log) {

		if (!log.isDebugEnabled() || data == null)
			return;

		try {
			final StringBuilder buf = new StringBuilder();

			if (label != null) {
				buf.append(label).append(LINE_SEP);
			}
			buf.append(dump(data));

			log.debug(buf.toString());

		} catch (final Exception e) {
			log.warn("Failed to log hex/ascii data (" + label + ")", e);
		}
    }

	/**
	 * Hidden constructor
	 */
	private GWLogger() {
	}

}
