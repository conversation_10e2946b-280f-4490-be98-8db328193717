package cz.wincor.ovv.viewer.utils;

import cz.wincor.ovv.viewer.security.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.AccessDeniedException;

public class SyslogAudit {

    public static final String APP_NAME = "OVV-VIEWER-B<PERSON>LA";

    public enum Event {
        LOGIN_SUCCESS("LOGIN.SUCCESS"),

        LOGIN_FAIL("LOGIN.FAIL"),

        LOGOUT("LOGOUT"),

        CREDENTIALSMANIPULATION_INSERT("CREDENTIALSMANIPULATION.INSERT"),

        CREDENTIALSMANIPULATION_UPDATE("CREDENTIALSMANIPULATION.UPDATE"),

        CREDENTIALSMANIPULATION_DELETE("CREDENTIALSMANIPULATION.DELETE");

        private String eventName;

        Event(String eventName) {
            this.eventName = eventName;
        }


    }

    private static final Logger LOG = LoggerFactory.getLogger("audit");

    public static void audit(Event event, String origin, String message) {


        String loggedUser = null;
        try {
            loggedUser = SecurityUtils.getLoggedUser().getUsername();
        } catch (AccessDeniedException e) {
            loggedUser = "";
        }
        LOG.info("{};{};" + APP_NAME + ";{};{}",loggedUser, event.eventName, origin, message);
    }
}
