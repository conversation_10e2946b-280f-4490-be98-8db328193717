package cz.wincor.ovv.viewer.utils;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.Validate;

import cz.wincor.ovv.viewer.xsd.CountryCode;

public class StoreUtil {

    private static final Map<Integer, CountryCode> countryCodesToPrefixes = new HashMap<>();

    static {
        countryCodesToPrefixes.put(1, CountryCode.CZ);
        countryCodesToPrefixes.put(2, CountryCode.SK);
        countryCodesToPrefixes.put(3, CountryCode.PL);
        countryCodesToPrefixes.put(4, CountryCode.HU);
    }

    private StoreUtil() {
    }

    /**
     * Get {@link CountryCode} from the input store number
     *
     * @param storeNumber
     * @return {@link CountryCode}, null if no {@link CountryCode} found
     * @throws NullPointerException if storeNumber is null
     */
    public static final CountryCode getCountryCode(String storeNumber) {
        Validate.notNull(storeNumber, "OvvStore number cannot be null");
        if (storeNumber.length() == 5) {
            CountryCode countryCode = countryCodesToPrefixes.get(Integer.valueOf("" + storeNumber.charAt(0)));
            return countryCode;
        }
        return null;

    }

}
