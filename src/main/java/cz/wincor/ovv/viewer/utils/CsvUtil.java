package cz.wincor.ovv.viewer.utils;

import java.io.Reader;
import java.util.Locale;

import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;

import cz.wincor.ovv.viewer.pojo.CashRegisterImportEntry;

public class CsvUtil {

    /**
     * Gets OpenCsv bean converter for specified reader.
     *
     * @param fileReader
     * @return
     */
    public static CsvToBean<CashRegisterImportEntry> getBeanConverter(Reader fileReader) {
        CsvToBean<CashRegisterImportEntry> beanConverter = new CsvToBeanBuilder<CashRegisterImportEntry>(fileReader)
                .withType(CashRegisterImportEntry.class).withThrowExceptions(false).withErrorLocale(Locale.ROOT)
                .withSkipLines(1).withSeparator(';').build();
        return beanConverter;
    }

}
