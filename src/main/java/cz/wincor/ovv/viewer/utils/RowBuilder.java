package cz.wincor.ovv.viewer.utils;

import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.DateFormatConverter;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.Locale;

/**
 * Create rows at end of sheet, create cells at end of row.
 */
public class RowBuilder {
    private static final int FILTER_ICON_WIDTH_IN_CHARS = 3;
    private static final int WIDTH_UNITS_IN_ONE_CHAR = 256;

    private final Sheet sheet;
    private int nextRow;
    private int nextCell;
    private Row currentRow;
    private Cell currentCell;

    private final CellStyle dateTimeStyle;

    public RowBuilder(Sheet sheet, Workbook wb) {
        this.sheet = sheet;

        DataFormat dateTimeFormat = wb.createDataFormat();
        dateTimeStyle = wb.createCellStyle();
        dateTimeStyle.setDataFormat(dateTimeFormat.getFormat(DateFormatConverter.convert(Locale.getDefault(), "dd.MM.yyyy HH:mm:ss")));
    }

    public RowBuilder createRow() {
        currentRow = sheet.createRow(nextRow);
        nextRow++;
        nextCell = 0;
        return this;
    }

    public RowBuilder addCell(String data) {
        currentCell = currentRow.createCell(nextCell);
        nextCell++;

        if (data != null) {
            currentCell.setCellValue(data);
        }

        return this;
    }

    public RowBuilder addCell(Number data) {
        currentCell = currentRow.createCell(nextCell);
        nextCell++;

        if (data != null) {
            currentCell.setCellValue(data.doubleValue());
        }

        return this;
    }

    public RowBuilder addDateTime(LocalDateTime data) {
        currentCell = currentRow.createCell(nextCell);
        nextCell++;

        if (data != null) {
            Instant instant = data.atZone(ZoneId.systemDefault()).toInstant();
            currentCell.setCellValue(Date.from(instant));
        } else {
            currentCell.setCellValue((Date) null);
        }

        currentCell.setCellStyle(dateTimeStyle);

        return this;
    }

    public RowBuilder setWidth(int lengthInChars) {
        try {
            int units = WIDTH_UNITS_IN_ONE_CHAR * (lengthInChars + FILTER_ICON_WIDTH_IN_CHARS);
            sheet.setColumnWidth(currentCell.getAddress().getColumn(), units);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        return this;
    }

    public RowBuilder setWidth(int lengthInChars, int column) {
        try {
            int units = WIDTH_UNITS_IN_ONE_CHAR * (lengthInChars + FILTER_ICON_WIDTH_IN_CHARS);
            sheet.setColumnWidth(column, units);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        return this;
    }

    public RowBuilder setAutoFilter() {
        sheet.setAutoFilter(new CellRangeAddress(0, currentRow.getRowNum(), 0, currentCell.getColumnIndex()));
        return this;
    }

    public RowBuilder freezeHeaderRow() {
        sheet.createFreezePane(0, 1);
        return this;
    }

    public Row getCurrentRow() {
        return currentRow;
    }

    public Cell getCurrentCell() {
        return currentCell;
    }
}
