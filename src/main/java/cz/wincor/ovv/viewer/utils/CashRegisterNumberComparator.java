package cz.wincor.ovv.viewer.utils;

import java.util.Comparator;

import org.apache.commons.lang3.StringUtils;

import cz.wincor.ovv.viewer.model.entity.CashRegister;

/**
 * Compare with CashRegister.number only, if CashRegister.number is numeric, numeric sort will be used
 */
public class CashRegisterNumberComparator implements Comparator<CashRegister> {
    @Override
    public int compare(CashRegister o1, CashRegister o2) {
        //01 is numberic, o2 not, so the first will be text
        if (StringUtils.isNumeric(o1.getNumber()) && !StringUtils.isNumeric(o2.getNumber())) {
            return 1;
        }
        //01 is not numberic, o2 yes, so the first will be the number 2
        if (!StringUtils.isNumeric(o1.getNumber()) && StringUtils.isNumeric(o2.getNumber())) {
            return -1;
        }
        //Neither number is actually numberic, so compare as a string
        if (!StringUtils.isNumeric(o1.getNumber()) && !StringUtils.isNumeric(o2.getNumber())) {
            return o1.getNumber().compareTo(o2.getNumber());
        }
        //Both numbers are actually numbers, so compare as a number
        return Integer.valueOf(o1.getNumber()).compareTo(Integer.valueOf(o2.getNumber()));
    }
}
