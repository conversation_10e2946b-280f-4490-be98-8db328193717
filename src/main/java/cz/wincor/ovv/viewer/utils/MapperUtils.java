package cz.wincor.ovv.viewer.utils;

import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.data.domain.Page;

import cz.wincor.ovv.viewer.dto.DtoMapper;
import cz.wincor.ovv.viewer.dto.DtoPage;

/**
 * Mapping related utility methods.
 *
 * <AUTHOR>
 */
public class MapperUtils {

    /**
     * Map {@link Collection} of entities to a {@link List} of DTOs.
     *
     * @param collection {@link Collection} of entities. Never null.
     * @return {@link List} of mapped DTOs. Never null.
     */
    public static <T extends DtoMapper<D>, D> List<D> mapCollection(Collection<T> collection) {
        return mapCollection(collection, T::mapToDto);
    }

    /**
     * Map {@link Collection} of entities to a {@link List} of DTOs using the specified mapper.
     *
     * @param collection {@link Collection} of entities. Never null.
     * @param mapper Mapping function. Never null.
     * @return {@link List} of mapped DTOs. Never null.
     */
    public static <T, D> List<D> mapCollection(Collection<T> collection, Function<T, D> mapper) {
        return collection.stream().map(mapper).collect(Collectors.toList());
    }

    /**
     * Map a page of entities to a page of DTOs.
     *
     * @param page {@link Page} from Spring Data. Never null.
     * @return {@link DtoPage} with DTOs. Never null.
     */
    public static <T extends DtoMapper<D>, D> DtoPage<D> mapPage(Page<T> page) {
        return mapPage(page, T::mapToDto);
    }

    /**
     * Map a page of entities to a page of DTOs using the specified mapper.
     *
     * @param page {@link Page} from Spring Data. Never null.
     * @param mapper Mapping function. Never null.
     * @return {@link DtoPage} with DTOs. Never null.
     */
    public static <T, D> DtoPage<D> mapPage(Page<T> page, Function<T, D> mapper) {
        DtoPage<D> dtoPage = new DtoPage<>();
        dtoPage.setTotal(page.getTotalElements());
        dtoPage.setData(page.getContent().stream().map(mapper)
                .collect(Collectors.toList()));
        return dtoPage;
    }
}
