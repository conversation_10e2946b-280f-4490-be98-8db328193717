package cz.wincor.ovv.viewer.exception;

/**
 * Exception thrown when a domain object can not be found.
 *
 * <AUTHOR>
 */
public class NotFoundException extends Exception {

    private static final long serialVersionUID = 39305196041421284L;

    public NotFoundException() {
        super();
    }

    public NotFoundException(String objectType, Long objectId) {
        super("Object '" + objectType + "' with ID=" + objectId + " was not found.");
    }

    public NotFoundException(String message) {
        super(message);
    }
}
