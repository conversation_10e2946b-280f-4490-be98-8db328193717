package cz.wincor.ovv.viewer.exception;

import java.util.Collection;

import cz.wincor.ovv.viewer.validation.ValidationError;

public class ValidationException extends Exception {

    private static final long serialVersionUID = -2538329298707147084L;

    private Collection<ValidationError> errors;


    public ValidationException(Collection<ValidationError> errors) {
        super();
        this.errors = errors;
    }

    public ValidationException(String message) {
        super(message);
    }

    public ValidationException(String message, Collection<ValidationError> errors) {
        super(message);
        this.errors = errors;
    }

    public Collection<ValidationError> getErrors() {
        return errors;
    }
}
