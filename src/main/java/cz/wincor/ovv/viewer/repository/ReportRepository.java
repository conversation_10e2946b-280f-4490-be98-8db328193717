package cz.wincor.ovv.viewer.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import cz.wincor.ovv.viewer.model.ReportStatus;
import cz.wincor.ovv.viewer.model.entity.Report;


/**
 * Repository for {@link Report} entities.
 *
 * <AUTHOR>
 */
public interface ReportRepository extends JpaRepository<Report, Long>, QuerydslPredicateExecutor<Report> {

    List<Report> findByStatusOrderByCreatedDate(ReportStatus status);

}
