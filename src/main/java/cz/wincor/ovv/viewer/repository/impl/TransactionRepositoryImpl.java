package cz.wincor.ovv.viewer.repository.impl;


import com.querydsl.jpa.impl.JPAQueryFactory;
import cz.wincor.ovv.viewer.model.entity.Transaction;
import cz.wincor.ovv.viewer.repository.TransactionListRepository;
import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.JpaEntityInformationSupport;

public class TransactionRepositoryImpl extends SliceableRepositoryImpl<Transaction, Long> implements TransactionListRepository {

    private JPAQueryFactory factory;


    @Autowired
    public TransactionRepositoryImpl(EntityManager entityManager) {
        super((JpaEntityInformation<Transaction, Long>) JpaEntityInformationSupport.getEntityInformation(Transaction.class,
                entityManager), entityManager);
        this.factory = new JPAQueryFactory(entityManager);
    }
}

