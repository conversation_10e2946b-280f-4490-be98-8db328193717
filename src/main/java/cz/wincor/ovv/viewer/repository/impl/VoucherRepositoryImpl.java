package cz.wincor.ovv.viewer.repository.impl;


import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import cz.wincor.ovv.viewer.model.entity.QVoucher;
import cz.wincor.ovv.viewer.model.entity.Voucher;
import cz.wincor.ovv.viewer.repository.VoucherListRepository;
import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.JpaEntityInformationSupport;

import java.util.List;

public class VoucherRepositoryImpl extends SliceableRepositoryImpl<Voucher, Long> implements VoucherListRepository {

    private JPAQueryFactory factory;


    @Autowired
    public VoucherRepositoryImpl(EntityManager entityManager) {
        super((JpaEntityInformation<Voucher, Long>) JpaEntityInformationSupport.getEntityInformation(Voucher.class,
                entityManager), entityManager);
        this.factory = new JPAQueryFactory(entityManager);
    }

    @Override
    public List<Voucher> getVouchers(Predicate predicate, Pageable pageRequest) {
        QVoucher qVoucher = QVoucher.voucher;

        JPAQuery<Voucher> query = factory
                .select(qVoucher)
                .from(qVoucher)
                .leftJoin(qVoucher.relatedTransactionRequest).fetchJoin()
                .where(predicate);

        if (pageRequest != null) {
            querydsl.applyPagination(pageRequest, query);
        }
        return query.fetch();
    }


}

