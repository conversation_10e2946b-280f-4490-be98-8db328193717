package cz.wincor.ovv.viewer.repository;

import cz.wincor.ovv.viewer.model.entity.ReportFile;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ReportFileRepository extends JpaRepository<ReportFile, Long>,
        QuerydslPredicateExecutor<ReportFile> {

    @Query("SELECT f FROM ReportFile f where LOWER(f.fileName) LIKE LOWER(:fileName)")
    List<ReportFile> findAllByFileName(@Param("fileName") String fileName, Pageable pageable);
}
