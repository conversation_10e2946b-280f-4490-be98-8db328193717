package cz.wincor.ovv.viewer.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;

import cz.wincor.ovv.viewer.model.entity.CashRegister;

public interface CashRegisterRepository
        extends JpaRepository<CashRegister, Long>, QuerydslPredicateExecutor<CashRegister> {

    @Query("SELECT cs FROM CashRegister cs where cs.siteCode = :siteCode and cs.active = true and cs.number LIKE :number% order by cs.number")
    List<CashRegister> findAllBySiteCodeAndNumberFilter(@Param("siteCode") String siteCode,
            @Param("number") String number);

    @Modifying
    @Query("delete from CashRegister cs where active = false")
    int deleteNonActive();

    @Modifying
    @Query("delete from CashRegister cs where active = true")
    int deleteActive();

    @Modifying
    @Query("update CashRegister cs set active = :active")
    int updateActive(@Param("active") boolean active);
}
