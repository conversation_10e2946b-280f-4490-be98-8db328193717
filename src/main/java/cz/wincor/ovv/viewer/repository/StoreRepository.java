package cz.wincor.ovv.viewer.repository;

import cz.wincor.ovv.viewer.enums.CountryEnum;
import cz.wincor.ovv.viewer.model.entity.Store;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface StoreRepository extends JpaRepository<Store, Long>,
        QuerydslPredicateExecutor<Store> {

    @Query("SELECT s FROM Store s where LOWER(s.siteCode) LIKE LOWER(:store) or LOWER(s.name) LIKE LOWER(:store)")
    List<Store> findAllBySiteCodeOrName(@Param("store") String store, Pageable pageable);

    @Query("SELECT s FROM User u join u.stores s where u.id = (:userId) and (LOWER(s.siteCode) LIKE LOWER(:store) or LOWER(s.name) LIKE LOWER(:store))")
    List<Store> findUserStoresBySiteCodeOrName(@Param("userId") Long userId, @Param("store") String store, Pageable pageable);

    @Query("SELECT s FROM Store s where s.countryCode = :country and (LOWER(s.siteCode) LIKE LOWER(:store) or LOWER(s.name) LIKE LOWER(:store))")
    List<Store> findAllBySiteCodeOrNameAndCountry(@Param("store") String store, @Param("country") CountryEnum country, Pageable pageable);
}
