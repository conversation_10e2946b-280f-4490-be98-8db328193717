package cz.wincor.ovv.viewer.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import cz.wincor.ovv.viewer.model.entity.Transaction;

/**
 * Repository for {@link Transaction} entity.
 *
 * <AUTHOR>
 */
public interface TransactionRepository extends JpaRepository<Transaction, Long>, QuerydslPredicateExecutor<Transaction>, TransactionListRepository{

    // No additional functionality needed
}
