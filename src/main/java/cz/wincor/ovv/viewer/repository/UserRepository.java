package cz.wincor.ovv.viewer.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import cz.wincor.ovv.viewer.model.entity.User;

/**
 * Repository for {@link User} entity.
 *
 * <AUTHOR>
 */
public interface UserRepository extends JpaRepository<User, Long>,
        QuerydslPredicateExecutor<User> {

    /**
     * Find a user by username.
     * @param username Username to match.
     * @return Existing {@link User} or {@code null} if no user with the given username is registered.
     */
    User findByUsername(String username);
}
