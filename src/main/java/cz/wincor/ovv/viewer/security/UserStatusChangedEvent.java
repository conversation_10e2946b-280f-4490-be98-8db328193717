package cz.wincor.ovv.viewer.security;

import org.springframework.context.ApplicationEvent;

/**
 * Notification event about user logout.
 */
public class UserStatusChangedEvent extends ApplicationEvent {
    private String message;
    private boolean newStatus;

    public UserStatusChangedEvent(Object source, String message, boolean newStatus) {
        super(source);
        this.message = message;
        this.newStatus = newStatus;
    }
    public String getMessage() {
        return message;
    }

    public boolean isNewStatus() {
        return newStatus;
    }
}