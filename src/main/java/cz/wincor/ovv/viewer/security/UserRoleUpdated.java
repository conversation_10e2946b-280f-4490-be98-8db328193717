package cz.wincor.ovv.viewer.security;

import cz.wincor.ovv.viewer.dto.UserDto;
import cz.wincor.ovv.viewer.model.UserRole;
import org.springframework.context.ApplicationEvent;

/**
 * Notification event about user logout.
 */
public class UserRoleUpdated extends ApplicationEvent {
    private final UserRole newRole;
    private final UserRole oldRole;
    private String message;

    public UserRoleUpdated(Object source, String message, UserRole oldRole, UserRole newRole) {
        super(source);
        this.message = message;
        this.oldRole = oldRole;
        this.newRole = newRole;
    }

    public String getMessage() {
        return message;
    }

    public UserRole getNewRole() {
        return newRole;
    }

    public UserRole getOldRole() {
        return oldRole;
    }

}