package cz.wincor.ovv.viewer.security;

import cz.wincor.ovv.viewer.model.UserRole;
import org.springframework.context.ApplicationEvent;

/**
 * Notification event about user logout.
 */
public class UserPasswordChangedEvent extends ApplicationEvent {
    private String message;

    public UserPasswordChangedEvent(Object source, String message) {
        super(source);
        this.message = message;
    }
    public String getMessage() {
        return message;
    }
}