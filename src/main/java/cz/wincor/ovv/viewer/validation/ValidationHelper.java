package cz.wincor.ovv.viewer.validation;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import cz.wincor.ovv.viewer.exception.ValidationException;

/**
 * Validation helper. It can process and evaluate validation criteria and collect
 * validation errors.
 *
 * <AUTHOR>
 */
public class ValidationHelper {

    private final List<ValidationError> errors = new ArrayList<>();


    public boolean notBlank(String value, String field, String errorMessage) {
        if (StringUtils.isBlank(value)) {
            error(field, ErrorCode.STRING_BLANK, errorMessage);
            return false;
        }
        return true;
    }

    public boolean notNull(Object value, String field, String errorMessage) {
        if (value == null) {
            error(field, ErrorCode.OBJECT_NULL, errorMessage);
            return false;
        }
        return true;
    }

    public boolean notEmpty(Collection<? extends Object> values, String field, String errorMessage) {
        if (values == null || values.isEmpty()) {
            error(field, ErrorCode.COLLECTION_EMPTY, errorMessage);
            return false;
        }
        return true;
    }

    public boolean maxLength(String value, int maxLength, String field, String errorMessage) {
        if (value.length() > maxLength) {
            error(field, ErrorCode.STRING_TOO_LONG, errorMessage);
            return false;
        }
        return true;
    }

    public boolean between(long value, Long minValue, Long maxValue, String field, String errorMessage) {
        boolean valid = true;
        if (minValue != null && value < minValue) {
            error(field, ErrorCode.NUMBER_TOO_LOW, errorMessage);
            valid = false;
        }
        if (maxValue != null && value > maxValue) {
            error(field, ErrorCode.NUMBER_TOO_HIGH, errorMessage);
            valid = false;
        }
        return valid;
    }

    public boolean between(int value, Integer minValue, Integer maxValue, String field, String errorMessage) {
        return between((long) value,
                minValue == null ? null : minValue.longValue(),
                maxValue == null ? null : maxValue.longValue(),
                field, errorMessage);
    }

    public boolean isTrue(boolean condition, String field, String errorCode, String errorMessage) {
        if (!condition) {
            error(field, errorCode, errorMessage);
            return false;
        }
        return true;
    }

    public void error(String field, String errorCode, String errorMessage) {
        errors.add(new ValidationError(field, errorCode, errorMessage));
    }

    public void error(String field, ErrorCode errorCode, String errorMessage) {
        error(field, errorCode.getCodeKey(), errorMessage);
    }

    /**
     * Check if there are any validation errors.
     * @throws ValidationException if the list of validation errors is not empty.
     */
    public void checkErrors() throws ValidationException {
        if (!errors.isEmpty()) {
            throw new ValidationException(errors);
        }
    }
}
