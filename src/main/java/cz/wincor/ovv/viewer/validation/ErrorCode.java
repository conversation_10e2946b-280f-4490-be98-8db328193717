package cz.wincor.ovv.viewer.validation;

/**
 * Enumeration of common error codes.
 *
 * <AUTHOR>
 */
public enum ErrorCode {

    /**
     * Error of a blank (empty or only whitespaces) string.
     */
    STRING_BLANK("string.blank"),
    /**
     * Error of a string which exceeds the maximum allowed length.
     */
    STRING_TOO_LONG("string.tooLong"),
    /**
     * Error of a {@code null} object.
     */
    OBJECT_NULL("object.null"),
    /**
     * Error of an invalid object value.
     */
    OBJECT_INVALID("object.invalid"),
    /**
     * Error of a duplicate object.
     */
    OBJECT_DUPLICATE("object.duplicate"),
    /**
     * Error of an empty collection of values.
     */
    COLLECTION_EMPTY("collection.empty"),
    /**
     * Error of a number which is below the minimum value.
     */
    NUMBER_TOO_LOW("number.tooLow"),
    /**
     * Error of a number which is above the maximum value.
     */
    NUMBER_TOO_HIGH("number.tooHigh");


    private final String codeKey;

    private ErrorCode(String codeKey) {
        this.codeKey = codeKey;
    }

    /**
     * Get code key of the error.
     */
    public String getCodeKey() {
        return codeKey;
    }
}
