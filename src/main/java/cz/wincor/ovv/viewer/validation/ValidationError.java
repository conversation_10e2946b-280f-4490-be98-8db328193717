package cz.wincor.ovv.viewer.validation;

/**
 * Validation error.
 *
 * <AUTHOR>
 */
public class ValidationError {

    /**
     * Designation of the field for which the error was detected.
     */
    private String field;
    /**
     * Error code. Can be used for l10n purposes.
     */
    private String code;
    /**
     * Error message.
     */
    private String message;


    /**
     * Default empty constructor.
     */
    public ValidationError() {
    }

    public ValidationError(String field, ErrorCode errorCode, String message) {
        this(field, errorCode.getCodeKey(), message);
    }

    public ValidationError(String field, String code, String message) {
        this.field = field;
        this.code = code;
        this.message = message;
    }

    @Override
    public String toString() {
        return "field=" + field + ", code=" + code + ", message=" + message;
    }

    public String getField() {
        return field;
    }
    public void setField(String field) {
        this.field = field;
    }

    public String getCode() {
        return code;
    }
    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }
    public void setMessage(String message) {
        this.message = message;
    }
}
