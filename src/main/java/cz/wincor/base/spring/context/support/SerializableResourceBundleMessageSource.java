package cz.wincor.base.spring.context.support;

import java.util.Locale;
import java.util.Properties;

import org.springframework.context.support.ReloadableResourceBundleMessageSource;

/**
 * Extended {@link ReloadableResourceBundleMessageSource} supporting access to all
 * properties for a specific locale.
 *
 * <p>See https://gist.github.com/rvillars/6422287
 *
 * <AUTHOR>
 */
public class SerializableResourceBundleMessageSource extends ReloadableResourceBundleMessageSource {

    public Properties getAllProperties(Locale locale) {
        clearCacheIncludingAncestors();
        PropertiesHolder propertiesHolder = getMergedProperties(locale);
        Properties properties = propertiesHolder.getProperties();
        return properties;
    }
}
