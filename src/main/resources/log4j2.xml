<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="debug" name="ovv-viewer">
    <Properties>
        <property name="pattern">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %X{user} %msg [%logger{1.}]%n</property>
        <Property name="baseDir">${sys:ovvViewerLoggingPath:-${sys:catalina.base:-/tmp}/logs}</Property>
    </Properties>

    <Appenders>
        <RollingFile name="RollingFile" fileName="${baseDir}/ovv-viewer-billa.log"
            filePattern="${baseDir}/ovv-viewer-billa-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${pattern}" />
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="100 MB" />
            </Policies>
            <DefaultRolloverStrategy max="90" />
        </RollingFile>
        <Syslog name="syslog-audit" host="localhost" port="514" protocol="UDP"/>
    </Appenders>


    <Loggers>
        <Logger name="cz.wincor" level="DEBUG" />
        <Logger name="audit" level="INFO">
            <AppenderRef ref="RollingFile" />
            <AppenderRef ref="syslog-audit"/>
        </Logger>
        <Root level="INFO">
            <AppenderRef ref="RollingFile" />
        </Root>
    </Loggers>
</Configuration>
