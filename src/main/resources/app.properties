# Maximum number of transactions for export to XLS file
app.export.maxSize = 65000

# Batch size for transaction export to XLS file
app.export.batchSize = 1000

#Maximum range in days used for "Used vouchers" report
app.report.maxDaysRange = 40

# Number of stores in search window
app.store.searchSize = 10

# Number of report files in search window
app.batch.fileNameSearchSize = 10

# Format of dates in UI (see http://momentjs.com/docs/#/displaying/format/)
ovv.ui.dateFormat = D.M.YYYY


#Path to a key store, which contains information about server's certificate
#Important for HTTPS connections only
#If empty value, default system certificate store is used
#Default value = security/cacerts.jks
#remoteHost.keyStore.path = security/cacerts.jks

#Key store password
#Default value = Wincor2015
#remoteHost.keyStore.password = Wincor2015

#Key store type
#Possible values (case insensitive):
#     * JCEKS The proprietary keystore implementation provided by the SunJCE provider.
#     * JKS The proprietary keystore implementation provided by the SUN provider.
#     * PKCS12 The transfer syntax for personal identity information as defined in PKCS #12.
#Default value = JKS
#remoteHost.keyStore.type = JKS

#Security algorithm
#Possible values:
#     * SSL Supports some version of SSL; may support other versions
#     * SSLv2 Supports SSL version 2 or later; may support other versions
#     * SSLv3 Supports SSL version 3; may support other versions
#     * TLS Supports some version of TLS; may support other versions
#     * TLSv1 Supports RFC 2246: TLS version 1.0 ; may support other versions
#     * TLSv1.1 Supports RFC 4346: TLS version 1.1 ; may support other versions
#     * TLSv1.2 Supports RFC 5246: TLS version 1.2 ; may support other versions
#Default value = TLS
#remoteHost.security.algorithm = TLS
remoteHost.request.sendSecure = false
remoteHost.request.url = http://localhost:12000/receiveGateway/ovv
remoteHost.request.contentType = application/xml;charset=utf-8

# Current version of ovv
app.version = ${project.version}

# Maximal time span for search of transaction (in days)
ovv.transactionSearch.maximalPeriod = 180

# how often will the import of cash registr definition execute
cron.cashRegisterExpression = 0 1 * * * ?

# settings for cash register import
cashRegisterImport.downloadFolder=${java.io.tmpdir}/ovv/work
cashRegisterImport.archiveFolder=${java.io.tmpdir}/ovv/archive
cashRegisterImport.sftp.folder=/work
cashRegisterImport.sftp.archiveFolder=/archive
# commit batch size
cashRegisterImport.batchSize = 1000

# sftp setup
sftp.host=localhost
sftp.port=22
sftp.username=test
sftp.password=test
sftp.privateKey=
sftp.privateKeyPassphrase=

# Report processing settings
reports.queueSize = 1000
reports.threads = 3

