<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <!-- Definition of the request -->
    <xs:element name="request">
        <xs:complexType>
            <xs:all>
                <!-- Transaction type request -->
                <xs:element type="transtype_type" name="transtype" />
                <!-- POS identification -->
                <xs:element type="posid_type" name="posid" />
                <!-- POS local date time -->
                <xs:element type="xs:dateTime" name="posdatetime" />
                <!-- date time of the concentrator, may be empty -->
                <xs:element type="xs:dateTime" name="serverdatetimeutc"
                    minOccurs="0" />
                <!-- POS internal transaction counter or receipt number -->
                <xs:element type="stan_type" name="stan" />
                <!-- POS internal transaction counter or receipt number -->
                <xs:element type="countrycode_type" name="countrycode" />
                <!-- Request repeat flag, default false -->
                <xs:element type="xs:boolean" name="rrflag" minOccurs="0"
                    default="false" />
                <!-- Identification of the partner -->
                <xs:element type="partnerid_type" name="partnerid"
                    minOccurs="0" />
                <!-- Identification of the payment place, e.g. store id etc. -->
                <xs:element type="paymentplace_type" name="paymentplace" />
                <!-- voucher number to process -->
                <xs:element type="vouchernumber_type" name="vouchernumber" />
                <!-- voucher amount, if cannot be read from voucher number -->
                <xs:element type="amount_type" name="amount" minOccurs="0" />
                <!-- Offline approval flag, default false -->
                <xs:element type="xs:boolean" name="offline" minOccurs="0"
                    default="false" />
                <!-- currency code of the amount, required only if amount is present -->
                <xs:element type="poscurrencycode_type" name="poscurrencycode"
                    minOccurs="0" />
                <!-- a country code of the POS -->
                <xs:element type="poscountrycode_type" name="poscountrycode" />
                <xs:element type="xs:boolean" name="trainingmode"
                    minOccurs="0" default="false" />
                <xs:element type="reversalreason_type" name="reversalreason"
                    minOccurs="0" />
                <!-- Operator -->
                <xs:element type="operator_type" name="operator"
                    minOccurs="0" />
                <!-- Manual redemption -->
                <xs:element type="xs:boolean" name="manualRedemption"
                            minOccurs="0" />
            </xs:all>
        </xs:complexType>
    </xs:element>
    <!-- Definition of the response -->
    <xs:element name="response">
        <xs:complexType>
            <xs:all>
                <!-- Transaction type for response -->
                <xs:element type="transtype_type" name="transtype" />
                <!-- Echoed value from the request -->
                <xs:element type="posid_type" name="posid" />
                <!-- Echoed value from the request -->
                <xs:element type="xs:dateTime" name="posdatetime" />
                <xs:element type="xs:dateTime" name="serverdatetimeutc" />
                <!-- Echoed value from the request -->
                <xs:element type="stan_type" name="stan" />
                <!-- Echoed value from request -->
                <xs:element type="partnerid_type" name="partnerid"
                    minOccurs="0" />
                <!-- Echoed value from request -->
                <xs:element type="paymentplace_type" name="paymentplace" />
                <!-- Server's id of the current transaction -->
                <xs:element type="transactionid_type" name="transactionid"
                    minOccurs="0" />
                <!-- Result of the request -->
                <xs:element type="resultcode_type" name="resultcode" />
                <!-- Error description if any -->
                <xs:element type="xs:string" name="errordescription"
                    minOccurs="0" />
                <!-- validation host if any -->
                <xs:element type="xs:string" name="validationhost"
                    minOccurs="0" />
                <!-- found voucher's issuer code, if any -->
                <xs:element type="xs:string" name="issuercode" minOccurs="0" />
                <!-- found voucher's issuer code, if any -->
                <xs:element type="amount_type" name="amount" minOccurs="0" />
            </xs:all>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="transtype_type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="0100" /> <!-- CHECK -->
            <xs:enumeration value="0200" /> <!-- VALIDATION -->
            <xs:enumeration value="0400" /><!-- REVERSAL -->
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="countrycode_type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CZ" />
            <xs:enumeration value="PL" />
            <xs:enumeration value="HU" />
            <xs:enumeration value="SK" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="resultcode_type">
        <xs:restriction base="xs:string">
            <xs:enumeration value="00" /><!-- APPROVED -->
            <xs:enumeration value="07" /><!-- ALREADY_USED -->
            <xs:enumeration value="08" /><!-- ALREADY_USED_SAME_TRN -->
            <xs:enumeration value="10" /><!-- MESSAGE_FORMAT_ERROR -->
            <xs:enumeration value="11" /><!-- WRONG_REVERSAL -->
            <xs:enumeration value="12" /><!-- VOUCHER_NOT_FOUND -->
            <xs:enumeration value="13" /><!-- ISSUER_NOT_FOUND -->
            <xs:enumeration value="14" /><!-- TIMEOUT -->
            <xs:enumeration value="15" /><!-- WRONG_REPEATED_REQUEST -->
            <xs:enumeration value="16" /><!-- WRONG_VOUCHER_STATE -->
            <xs:enumeration value="17" /><!-- WRONG_STORE -->
            <xs:enumeration value="18" /><!-- STOLEN -->
            <xs:enumeration value="19" /><!-- NOT_ACCEPTED -->
            <xs:enumeration value="20" /><!-- AFTER_VALIDITY -->
            <xs:enumeration value="21" /><!-- SCAN_AGAIN -->
            <xs:enumeration value="22" /><!-- AMOUNT_DEF_NOT_FOUND -->
            <xs:enumeration value="50" /><!-- DECLINED -->
            <xs:enumeration value="99" /><!-- CANNOT_PROCESS -->
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="stan_type">
        <xs:restriction base="xs:int">
            <xs:minInclusive value="0" />
            <xs:maxInclusive value="999999" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="vouchernumber_type">
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-zA-Z0-9]{3,255}" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="poscurrencycode_type">
        <xs:restriction base="xs:short">
            <xs:minInclusive value="0" />
            <xs:maxExclusive value="1000" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="poscountrycode_type">
        <xs:restriction base="xs:short">
            <xs:minInclusive value="0" />
            <xs:maxExclusive value="1000" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="reversalreason_type">
        <xs:restriction base="xs:short">
            <xs:minInclusive value="0" />
            <xs:maxExclusive value="1000" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="amount_type">
        <xs:restriction base="xs:long">
            <xs:minInclusive value="0" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="paymentplace_type">
        <xs:restriction base="xs:string">
            <xs:maxLength value="255" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="partnerid_type">
        <xs:restriction base="xs:string">
            <xs:maxLength value="255" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="posid_type">
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-zA-Z0-9-]{1,255}" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="transactionid_type">
        <xs:restriction base="xs:long">
            <xs:minInclusive value="0" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="operator_type">
        <xs:restriction base="xs:string">
            <xs:maxLength value="255" />
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
