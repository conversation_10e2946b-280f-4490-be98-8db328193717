<?xml version="1.0" encoding="UTF-8"?>
<jaxb:bindings xmlns:xs="http://www.w3.org/2001/XMLSchema"
    xmlns:jaxb="https://jakarta.ee/xml/ns/jaxb" xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc"
    jaxb:extensionBindingPrefixes="xjc" version="3.0">
    <jaxb:globalBindings generateIsSetMethod="true"
        fixedAttributeAsConstantProperty="true">
        <xjc:serializable />
    </jaxb:globalBindings>
    <jaxb:bindings schemaLocation="schema.xsd">
        <jaxb:bindings
            node="//*/xs:element[@name='request']//*/xs:element[@name='transtype']">
            <jaxb:property name="transType" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='request']//*/xs:element[@name='posid']">
            <jaxb:property name="posId" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='request']//*/xs:element[@name='posdatetime']">
            <jaxb:property name="posDatetime" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='request']//*/xs:element[@name='serverdatetimeutc']">
            <jaxb:property name="serverDatetimeUtc" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='request']//*/xs:element[@name='rrflag']">
            <jaxb:property name="rrFlag" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='request']//*/xs:element[@name='countrycode']">
            <jaxb:property name="countryCode" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='request']//*/xs:element[@name='partnerid']">
            <jaxb:property name="partnerId" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='request']//*/xs:element[@name='paymentplace']">
            <jaxb:property name="paymentPlace" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='request']//*/xs:element[@name='vouchernumber']">
            <jaxb:property name="voucherNumber" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='request']//*/xs:element[@name='poscurrencycode']">
            <jaxb:property name="posCurrencyCode" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='request']//*/xs:element[@name='poscountrycode']">
            <jaxb:property name="posCountryCode" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='request']//*/xs:element[@name='trainingmode']">
            <jaxb:property name="trainingMode" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='request']//*/xs:element[@name='reversalreason']">
            <jaxb:property name="reversalReason" />
        </jaxb:bindings>

        <jaxb:bindings
            node="//*/xs:element[@name='response']//*/xs:element[@name='transtype']">
            <jaxb:property name="transType" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='response']//*/xs:element[@name='posid']">
            <jaxb:property name="posId" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='response']//*/xs:element[@name='posdatetime']">
            <jaxb:property name="posDatetime" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='response']//*/xs:element[@name='serverdatetimeutc']">
            <jaxb:property name="serverDatetimeUtc" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='response']//*/xs:element[@name='partnerid']">
            <jaxb:property name="partnerId" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='response']//*/xs:element[@name='paymentplace']">
            <jaxb:property name="paymentPlace" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='response']//*/xs:element[@name='transactionid']">
            <jaxb:property name="transactionId" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='response']//*/xs:element[@name='resultcode']">
            <jaxb:property name="resultCode" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='response']//*/xs:element[@name='errordescription']">
            <jaxb:property name="errorDescription" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='response']//*/xs:element[@name='validationhost']">
            <jaxb:property name="validationHost" />
        </jaxb:bindings>
        <jaxb:bindings
            node="//*/xs:element[@name='response']//*/xs:element[@name='issuercode']">
            <jaxb:property name="issuerCode" />
        </jaxb:bindings>

        <jaxb:bindings node="//xs:simpleType[@name='transtype_type']">
            <jaxb:typesafeEnumClass name="TransType" />
            <jaxb:bindings
                    node="//xs:simpleType[@name='transtype_type']/xs:restriction/xs:enumeration[@value='0100']">
                <jaxb:typesafeEnumMember name="CHECK" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='transtype_type']/xs:restriction/xs:enumeration[@value='0200']">
                <jaxb:typesafeEnumMember name="VALIDATION" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='transtype_type']/xs:restriction/xs:enumeration[@value='0400']">
                <jaxb:typesafeEnumMember name="REVERSAL" />
            </jaxb:bindings>
        </jaxb:bindings>
        <jaxb:bindings node="//xs:simpleType[@name='resultcode_type']">
            <jaxb:typesafeEnumClass name="ResultCode" />
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='00']">
                <jaxb:typesafeEnumMember name="APPROVED" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='07']">
                <jaxb:typesafeEnumMember name="ALREADY_USED" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='08']">
                <jaxb:typesafeEnumMember name="ALREADY_USED_SAME_TRN" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='10']">
                <jaxb:typesafeEnumMember name="MESSAGE_FORMAT_ERROR" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='11']">
                <jaxb:typesafeEnumMember name="WRONG_REVERSAL" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='12']">
                <jaxb:typesafeEnumMember name="VOUCHER_NOT_FOUND" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='13']">
                <jaxb:typesafeEnumMember name="ISSUER_NOT_FOUND" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='14']">
                <jaxb:typesafeEnumMember name="TIMEOUT" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='15']">
                <jaxb:typesafeEnumMember name="WRONG_REPEATED_REQUEST" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='16']">
                <jaxb:typesafeEnumMember name="WRONG_VOUCHER_STATE" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='17']">
                <jaxb:typesafeEnumMember name="WRONG_STORE" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='18']">
                <jaxb:typesafeEnumMember name="STOLEN" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='19']">
                <jaxb:typesafeEnumMember name="NOT_ACCEPTED" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='20']">
                <jaxb:typesafeEnumMember name="AFTER_VALIDITY" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='21']">
                <jaxb:typesafeEnumMember name="SCAN_AGAIN" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='22']">
                <jaxb:typesafeEnumMember name="AMOUNT_DEF_NOT_FOUND" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='50']">
                <jaxb:typesafeEnumMember name="DECLINED" />
            </jaxb:bindings>
            <jaxb:bindings
                node="//xs:simpleType[@name='resultcode_type']/xs:restriction/xs:enumeration[@value='99']">
                <jaxb:typesafeEnumMember name="CANNOT_PROCESS" />
            </jaxb:bindings>
        </jaxb:bindings>
        <jaxb:bindings node="//xs:simpleType[@name='countrycode_type']">
            <jaxb:typesafeEnumClass name="CountryCode" />
        </jaxb:bindings>

    </jaxb:bindings>
</jaxb:bindings>
