# Global application messages
app.title = Billa OVV tranzakci\u00F3 megtekint\u0151
app.name = OVV tranzakci\u00F3 megtekint\u0151
app.fullName = OVV megtekint\u0151
app.shortName = OVV

# Navigation bar
navbar.myAccount = Saj\u00E1t sz\u00E1ml\u00E1m
navbar.changePassword = Jelsz\u00F3 v\u00E1ltoztat\u00E1s
navbar.logout = Kijelentkez\u00E9s

# Login page
login.title = OVV tranzakci\u00F3 megtekint\u0151
login.header =Bejelentkez\u0151 az OVV megtekint\u0151be
login.username = Felhaszn\u00E1l\u00F3n\u00E9v
login.password = Jelsz\u00F3
login.submit = Bejelentkez\u00E9s
login.invalidCredentials=\u00C9rv\u00E9nytelen felhaszn\u00E1l\u00F3n\u00E9v \u00E9s/vagy jelsz\u00F3
login.loggedOut = Kijelentkeztetve
login.sessionExpired = A rendelkez\u00E9sre \u00E1ll\u00F3 id\u0151keret lej\u00E1rt. K\u00E9rj\u00FCk jelentkezzen be \u00FAjra.

# Menu
menu.manualRedemption = Manu\u00E1lis
menu.transactions = Tranzakci\u00F3
menu.administration =Adminisztr\u00E1tor
menu.administration.users = Felhaszn\u00E1l\u00F3
menu.app.version =Verzi\u00F3
menu.batch = k\u00F6teg
menu.batch.batchFileDetail =k\u00F6teg f\u00E1jl adatok
menu.batch.batchFileSummary = K\u00F6teg f\u00E1jl \u00F6sszes\u00EDt\u0151
menu.reports = Jelent\u00E9sek
menu.reports.xlsReports = XLS jelent\u00E9sek

# Common DataTables labels
datatables.noData = Adat nem tal\u00E1lhat\u00F3
datatables.show = _START_ - _END_
datatables.show.empty=Adat nem tal\u00E1lhat\u00F3
datatables.lengthMenu=Mutat _MENU_ bevitel
datatables.paginate.next =K\u00F6vetkez\u0151
datatables.paginate.previous = el\u0151z\u0151
datatables.processing =Keres\u00E9s ...

# Common UI component labels
common.filter =sz\u0171r\u00E9s
common.filter.search =Keres\u00E9s
common.filter.reset = sz\u0171r\u00E9si felt\u00E9telek vissza\u00E1ll\u00EDt\u00E1sa
common.filter.ALL =Mind
common.filter.YES =igen
common.filter.NO =Nem
common.filter.dateFrom = OVV D\u00E1tumt\u00F3l
common.filter.dateTo = OVV D\u00E1tumog
common.filter.createdFrom =l\u00E9trehozott ...-b\u0151l
common.filter.createdTo =L\u00E9trehozott ....-nek
common.filter.xlsExport =Export\u00E1l\u00E1s excelbe
common.filter.expirationYear = Lej\u00E1rat
common.filter.trainingMode = Tr\u00E9ning tranzakci\u00F3

# Common errors
common.error.invalidRequest = \u00E9rv\u00E9nytelen k\u00E9r\u00E9s elutas\u00EDtva

# Forms
form.field.error.required = K\u00F6telez\u0151 mez\u0151
form.field.error.tooLong = \u00C9z az \u00E9rt\u00E9k t\u00FAl hossz\u00FA
form.field.error.lesser = Ennek az \u00E9rt\u00E9knek nagyobbnak kell lenni, mint
form.field.error.invalidFormat =ez az \u00E9rt\u00E9k \u00E9rv\u00E9nytelen
form.number.error.notNumber = Az \u00E9rt\u00E9knek sz\u00E1madatnak kell lennie
form.number.error.invalid =Nem \u00E9rv\u00E9nyes sz\u00E1m
form.number.error.gt = Az \u00E9rt\u00E9knek nagyobbnak kell lenni, mint...
form.number.error.loe = Az \u00E9rt\u00E9k kevesebb, vagy egyenl\u0151, mint...
form.password.error.tooShort =A jelsz\u00F3nak minimum 8 karaktert kell tartalmaznia
form.password.error.digits = A jelsz\u00F3nak legal\u00E1bb 1 sz\u00E1mot
form.password.error.lowercase =A jelsz\u00F3nak legal\u00E1bb egy kisbet\u0171t kell tartalmaznia
form.password.error.uppercase =A jelsz\u00F3nak legal\u00E1bb egy nagybet\u0171t kell tartalmaznia
form.password.error.match =A jelszavak nem egyeznek
form.field.error.dateIsInPast = A d\u00E1tum a m\u00FAltra vonatkozik
form.save = V\u00E1ltoz\u00E1sok ment\u00E9se
form.cancel = T\u00F6rl\u00E9s
form.send = K\u00FCld\u00E9s

# Messages comopnent
comp.messages.info=Inform\u00E1ci\u00F3:
comp.messages.success=Sikeres:
comp.messages.warning=Figyelmeztet\u00E9s:
comp.messages.error=Hiba:

# Transaction List
transactions.list.heading=OVV tranzakci\u00F3

# Administration - Users
administration.users.list.heading =felhaszn\u00E1l\u00F3k
administration.users.list.addUser =Felhaszn\u00E1l\u00F3 hozz\u00E1ad\u00E1sa
administration.users.editUser =Felhaszn\u00E1l\u00F3k szerkeszt\u00E9se
administration.users.changePassword =Jelsz\u00F3 megv\u00E1ltoztat\u00E1sa
administration.users.disableUser = Felhaszn\u00E1l\u00F3 letiltva
administration.users.enableUser = Felhaszn\u00E1l\u00F3i jogosults\u00E1g vissza\u00E1ll\u00EDt\u00E1sa
administration.users.add.heading =Felhaszn\u00E1l\u00F3 hozz\u00E1ad\u00E1sa
administration.users.add.title =\u00DAj felhaszn\u00E1l\u00F3 adatai
administration.users.edit.heading =Felhaszn\u00E1l\u00F3 szerkeszt\u00E9se
administration.users.edit.title=Felhaszn\u00E1l\u00F3 adatai {{username}}
administration.users.info.created = \u00DAj felhaszn\u00E1l\u00F3 sikeresen l\u00E9trehozva
administration.users.info.updated = Felhaszn\u00E1l\u00F3 sikeresen friss\u00EDtve
administration.users.error.usernameTaken = A be\u00EDrt felhaszn\u00E1l\u00F3n\u00E9v m\u00E1r haszn\u00E1latban van, v\u00E1lassz egy m\u00E1sikat
administration.users.enableUser.success = Felhaszn\u00E1l\u00F3 {{username}} jogosults\u00E1ga vissza\u00E1ll\u00EDtva
administration.users.disableUser.success=Felhaszn\u00E1l\u00F3 {{username}} jogosults\u00E1ga letiltva
administration.users.changePassword.success = {{username}} felhaszn\u00E1l\u00F3 jelszava megv\u00E1ltozott.

# Transaction properties
transaction.serverDateTime=OVV D\u00E1tum, Id\u0151
transaction.deviceDateTime=POS D\u00E1tum, Id\u0151
transaction.countryCode = Orsz\u00E1g
transaction.deviceId = Eszk\u00F6z
transaction.stan =STAN
transaction.paymentPlace =Fizet\u00E9s helye
transaction.amount =\u00D6sszeg
transaction.type =T\u00EDpus
transaction.voucherNumber =N / Sz\u00E9riasz\u00E1m
transaction.ovvIssuer =Kibocs\u00E1t\u00F3
transaction.offlineMode = Offline
transaction.resultCode =Eredm\u00E9ny
transaction.trainingMode = Tr\u00E9ning tranzakci\u00F3
transaction.store =\u00C1ruh\u00E1z
transaction.id = ID
transaction.detail =R\u00E9szletek
transaction.validationHost = \u00C9rv\u00E9nyes\u00EDt\u0151
transaction.partnerId =Partner
transaction.errorDescription =Tov\u00E1bbi visszajelz\u00E9s inform\u00E1ci\u00F3
transaction.dateOrderInvalid = D\u00E1tum \u2013t\u00F3l -ig
transaction.intervalToBig = Maximum tranzakci\u00F3k k\u00F6zti keres\u00E9si intervallum {{interval}} nap
transaction.intervalDateInFuture = D\u00E1tum nem vonatkozhat j\u00F6v\u0151beni id\u0151pontra
transaction.manualRedemption = Kiadott Utalv\u00E1nyok

# User properties
user.username =Felhaszn\u00E1l\u00F3n\u00E9v
user.name =N\u00E9v
user.firstName =Keresztn\u00E9v
user.lastName =Vezet\u00E9kn\u00E9v
user.email =E-mail
user.role = Munkak\u00F6r
user.active =Akt\u00EDv
user.password = Jelsz\u00F3
user.passwordConfirm =Jelsz\u00F3 meger\u0151s\u00EDt\u00E9se
user.store =\u00C1ruh\u00E1z
user.countryCode = Orsz\u00E1g

#
# Code lists
#

# Transaction Types
transaction.type.VALIDATION = \u00C9rv\u00E9nyes\u00EDt\u00E9s
transaction.type.CHECK = \u00C9rv\u00E9nyes\u00EDt\u00E9s
transaction.type.REVERSAL = Visszaford\u00EDt\u00E1s

# Result Codes
transaction.resultCode.APPROVED = J\u00F3v\u00E1hagyva
transaction.resultCode.ALREADY_USED = M\u00E1r felhaszn\u00E1lva
transaction.resultCode.MESSAGE_FORMAT_ERROR = Form\u00E1tum hiba
transaction.resultCode.WRONG_REVERSAL = Rossz visszford\u00EDt\u00E1s
transaction.resultCode.VOUCHER_NOT_FOUND = Nem tal\u00E1lhat\u00F3
transaction.resultCode.ISSUER_NOT_FOUND = Ismeretlen kibocs\u00E1t\u00F3
transaction.resultCode.TIMEOUT = Id\u0151 lej\u00E1rt
transaction.resultCode.WRONG_REPEATED_REQUEST = Rossz ism\u00E9tl\u0151d\u0151 k\u00E9r\u00E9s
transaction.resultCode.WRONG_VOUCHER_STATE = Rossz utalv\u00E1ny st\u00E1tusz
transaction.resultCode.WRONG_STORE = Helytelen \u00E1ruh\u00E1z
transaction.resultCode.DECLINED=Kibocs\u00E1t\u00F3 \u00E1ltal letiltva
transaction.resultCode.CANNOT_PROCESS = Nem lehet feldolgozni
transaction.resultCode.SCAN_AGAIN  = Szkennelje \u00FAjra
transaction.resultCode.STOLEN = Lopott
transaction.resultCode.ALREADY_USED_SAME_TRN = M\u00E1r felhaszn\u00E1lva ebben a tranzakci\u00F3ban
transaction.resultCode.AFTER_VALIDITY = \u00C9rv\u00E9nyess\u00E9g lej\u00E1rt
transaction.resultCode.AMOUNT_DEF_NOT_FOUND=Nem tal\u00E1lhat\u00F3 \u00F6sszeg
transaction.resultCode.NOT_ACCEPTED=Nem elfogadott
# User Roles
user.role.ADMIN = Adminisztr\u00E1tor
user.role.VIEWER = Betekint\u0151
user.role.MANUAL_REDEMPTION = Manu\u00E1lis bev\u00E1lt\u00E1s
user.role.BATCH = K\u00F6teg
user.role.STORE_MANAGER = Manager

# Countries
country.PL = PL
country.CZ = CZ
country.SK = SK
country.HU = HU

#Manual Redemption
redemption.heading = Manu\u00E1lis utalv\u00E1ny bev\u00E1lt\u00E1s
redemption.transactionType = Tranzakci\u00F3 t\u00EDpusa
redemption.voucherNumber = Utalv\u00E1ny sz\u00E1ma
redemption.amount = \u00D6sszeg
redemption.store = \u00C1ruh\u00E1z
redemption.device = Eszk\u00F6z
redemption.stan =STAN
redemption.validation.APPROVED = Az utalv\u00E1ny sikeresen j\u00F3v\u00E1 lett hagyva.
redemption.validation.ALREADY_USED = Az utalv\u00E1ny m\u00E1r fel lett haszn\u00E1lva.
redemption.validation.MESSAGE_FORMAT_ERROR = Rossz hiba\u00FCzenet form\u00E1tum. Fordulj az adminisztr\u00E1torhoz.
redemption.validation.WRONG_REVERSAL = Az utalv\u00E1nyt nem lehet visszaford\u00EDtani.
redemption.validation.VOUCHER_NOT_FOUND = Az utalv\u00E1ny ne tal\u00E1lhat\u00F3.
redemption.validation.ISSUER_NOT_FOUND = Az utalv\u00E1ny kibocs\u00E1jt\u00F3ja nem tal\u00E1lhat\u00F3.
redemption.validation.TIMEOUT = A k\u00E9r\u00E9s id\u0151t\u00FAll\u00E9p\u00E9s miatt visszautas\u00EDtva. Pr\u00F3b\u00E1lja \u00FAjra.
redemption.validation.WRONG_REPEATED_REQUEST = Rossz ism\u00E9tl\u0151d\u0151 k\u00E9r\u00E9s, Fordulj az adminisztr\u00E1torhoz.
redemption.validation.WRONG_VOUCHER_STATE=Az utalv\u00E1ny sikeresen j\u00F3v\u00E1 lett hagyva.
redemption.validation.WRONG_STORE = Kiv\u00E1lasztott \u00E1ruh\u00E1z nem megfelel\u0151. V\u00E1laszd ki a helyes \u00E1ruh\u00E1zat.
redemption.validation.DECLINED=Kibocs\u00E1t\u00F3 \u00E1ltal letiltva.
redemption.validation.CANNOT_PROCESS = A k\u00E9r\u00E9st nem lehet v\u00E9grehajtani. Fordulj az adminisztr\u00E1torhoz.
redemption.validation.SCAN_AGAIN  = Szkennelje \u00FAjra.
redemption.validation.STOLEN = Lopott.
redemption.validation.ALREADY_USED_SAME_TRN = M\u00E1r felhaszn\u00E1lva ebben a tranzakci\u00F3ban.
redemption.validation.AFTER_VALIDITY = \u00C9rv\u00E9nyess\u00E9g lej\u00E1rt.
redemption.validation.AMOUNT_DEF_NOT_FOUND = Az utalv\u00E1nyt nem lehet automatikusan meghat\u00E1rozni.
redemption.validation.NOT_ACCEPTED=Nem elfogadott.
redemption.redemptionDateTime=D\u00E1tum
redemption.setAmount.heading=\u00C1ll\u00EDtsa be az utalv\u00E1ny \u00E9rt\u00E9k\u00E9t
redemption.setAmount.info=A voucher \u00E9rt\u00E9k\u00E9t nem lehet automatikusan meghat\u00E1rozni. \u00CDrja be k\u00E9zzel az \u00E9rt\u00E9ket.
redemption.confirmAmount=J\u00F3v\u00E1hagy\u00E1s

# Export
export.failed = Export\u00E1l\u00E1s sikertelen
export.tooBig = Export\u00E1lni k\u00EDv\u00E1nt f\u00E1jl t\u00FAl nagy, maxim\u00E1lis tranzakci\u00F3 sz\u00E1m 6500. V\u00E1ltoztasd meg a sz\u0171r\u00E9si felt\u00E9teleket \u00E9s pr\u00F3b\u00E1ld \u00FAjra.
batch.detail.heading = OVV k\u00F6teg f\u00E1jl adatok
batch.summary.heading = OVV k\u00F6teg f\u00E1jl \u00F6sszegz\u00E9s
batch.resultDescription = Eredm\u00E9ny le\u00EDr\u00E1sa
batch.created = L\u00E9trehozva
batch.fileName = F\u00E1jl neve
batch.action = Feladat
batch.detail = R\u00E9szletek
batch.reconciliated = Ellen\u0151rizve
batch.itemsCount = Lesz\u00E1molt t\u00E9telek
batch.summary.detail = K\u00F6teg adatok
batch.resultCode.0 = utalv\u00E1ny meger\u0151s\u00EDtve a kibocs\u00E1t\u00F3 \u00E1ltal
batch.resultCode.1 = Az utalv\u00E1ny nem lett meger\u0151s\u00EDtve, a kibocs\u00E1t\u00F3 \u00E1ltal, a kibocs\u00E1t\u00F3 bels\u0151 vizsg\u00E1lata sz\u00FCks\u00E9ges. A kibocs\u00E1t\u00F3 k\u00E9rheti az utalv\u00E1ny Billa \u00E1ltali rendelkez\u00E9sre bocs\u00E1t\u00E1s\u00E1t.
batch.resultCode.2 = Az utalv\u00E1ny nem lett meger\u0151s\u00EDtve, k\u00FCls\u0151 (rend\u0151rs\u00E9g, vayg m\u00E1s hat\u00F3s\u00E1g) kivizsg\u00E1l\u00E1sa sz\u00FCks\u00E9ges, Billa k\u00F6teles \u00E1tadni az utalv\u00E1nyt a megfelel\u0151 kibocs\u00E1t\u00F3nak.
batch.resultCode.3 = Az utalv\u00E1nyt a kibocs\u00E1t\u00F3 visszautas\u00EDtotta, az utalv\u00E1ny m\u00E1r egy m\u00E1sik partner \u00E1ltal bev\u00E1lt\u00E1sra ker\u00FClt.
batch.resultCode.4 = Az utalv\u00E1nyt a kibocs\u00E1t\u00F3 visszautas\u00EDtotta, az utalv\u00E1nyon l\u00E9v\u0151 k\u00F3d hib\u00E1s, hamis (nem l\u00E9tezik, vagy rossz).
batch.resultCode.5 =Az utalv\u00E1ny nem rendelkezik \u00E9rv\u00E9nyes telephely k\u00F3ddal, vagy k\u00F6lts\u00E9ghellyel (vagy hi\u00E1nyzik a telephelyk\u00F3d \u00E9s/vagy k\u00F6lts\u00E9ghely). Az \u00E9rv\u00E9nyess\u00E9g ellen\u0151rz\u00E9se megszak\u00EDtva, ameddig a hi\u00E1nyz\u00F3 adatok p\u00F3tl\u00E1sa megt\u00F6rt\u00E9nik.

batch.detail.title = Tranzakci\u00F3 r\u00E9szletek
batch.detail.close = Bez\u00E1r\u00E1s
batch.detail.notExistTransaction = Az "{{id}}" azonos\u00EDt\u00F3val rendelkez\u0151 tranzakci\u00F3 adatainak m\u00E9g nem \u00E1llnak rendelkez\u00E9sre, pr\u00F3b\u00E1lja meg k\u00E9s\u0151bb.
batch.costCentreSiteCode = K\u00F6lts\u00E9gk\u00F6zpont/\u00DCzleti sz\u00E1m

# Change password
users.changePassword.heading = Jelsz\u00F3 megv\u00E1ltoztat\u00E1sa
users.changePassword.title = Adja meg a felhaszn\u00E1l\u00F3 \u00FAj jelszav\u00E1t {{username}}.
users.changePassword.oldPassword = Eredeti jelsz\u00F3
users.changePassword.newPassword = \u00DAj jelsz\u00F3
users.changePassword.confirmNewPassword = Hagyja j\u00F3v\u00E1 az \u00FAj jelsz\u00F3t
changePassword.submit = Jelsz\u00F3 megv\u00E1ltoztat\u00E1sa

oldPassword.empty.passwordChangeForm.oldPassword = Nem lett megadva r\u00E9gi jelsz\u00F3
newPassword.empty.passwordChangeForm.newPassword = Nem lett megadva \u00FAj jelsz\u00F3
newPasswordConfirm.empty.passwordChangeForm.newPasswordConfirm = Hi\u00E1nyzik az \u00FAj jelsz\u00F3 j\u00F3v\u00E1hagy\u00E1sa.
newPassword.noMatch.passwordChangeForm.newPassword = Nem megfelel\u0151 \u00FAj jelsz\u00F3
newPassword.sameWithOld.passwordChangeForm.newPassword = A r\u00E9gi \u00E9s az \u00FAj jelsz\u00F3 egyforma
password.tooWeak.passwordChangeForm = Az \u00FAj jelsz\u00F3nak legal\u00E1bb 8 karakterb\u0151l kell \u00E1llnia (melyben legal\u00E1bb egy sz\u00E1mnak, egy kisbet\u0171nek \u00E9s egy nagybet\u0171nek kell lennie).
object.invalid.passwordChangeForm = Helytelen r\u00E9gi jelsz\u00F3

common.cancel = Visszavon\u00E1s

transaction.category = Kateg\u00F3ria
transaction.category.MEAL = \u00C9tkez\u00E9si utalv\u00E1ny
transaction.category.SOCIAL = Szoci\u00E1lis utalv\u00E1ny
transaction.category.GIFT = Aj\u00E1nd\u00E9k utalv\u00E1ny
transaction.category.SCHOOL = Iskolai
transaction.category.CHILDREN = Gyermek
transaction.category.OTHER = M\u00E1s

# Reports
reports.list.heading = XLS Jelent\u00E9sek
reports.list.createReport = \u00DAj jelent\u00E9s
reports.list.listTitle = Jelent\u00E9sek
reports.list.downloadReport = Jelent\u00E9s let\u00F6lt\u00E9se

reports.new.heading = \u00DAj jelent\u00E9s
reports.new.title = \u00DAj jelent\u00E9s tulajdons\u00E1gai
reports.new.parameters = Bel\u00E9p\u0151 param\u00E9terek
reports.new.info.success = Az \u00FAj jelent\u00E9s l\u00E9trehoz\u00E1sa \u00E9s feldolgoz\u00E1sra rendel\u00E9se megt\u00F6rt\u00E9nt.

# Report properties
report.type = T\u00EDpus
report.status = \u00C1llapot
report.store = \u00DCzlet
report.createdDate = L\u00E9trehoz\u00E1s d\u00E1tuma
report.params.dateFrom = D\u00E1tum ............-t\u00F3l/-t\u0151l
report.params.dateTo = D\u00E1tum .............-ig
report.params.store = \u00DCzlet

report.type.USED_VOUCHERS = Haszn\u00E1lt utalv\u00E1ny
report.type.CATEGORY=Kateg\u00F3ria jelent\u00E9s

# Report statuses
report.status.QUEUED = Feldolgoz\u00E1sra v\u00E1r
report.status.IN_PROGRESS = Feldolgoz\u00E1s alatt
report.status.OK = Befejezve
report.status.FAILED = Hiba
report.status.LARGE_EXPORT = T\u00FAlterhelt hat\u00E1r\u00E9rt\u00E9k

reports.dateInterval.invalid = D\u00E1tum \u2013t\u00F3l -ig
reports.dateInterval.tooBig = Meghaladta a maxim\u00E1lis intervallumot
