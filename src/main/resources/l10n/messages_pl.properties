# Global application messages
app.title = Billa OVV Przegl\u0105darka transakcji
app.name = OVV Przegl\u0105darka transakcji
app.fullName = Przegl\u0105darka OVV
app.shortName = OVV

# Navigation bar
navbar.myAccount = Moje konto
navbar.changePassword = Zmie\u0144 has\u0142o
navbar.logout = Wyloguj

# Login page
login.title = Przegladarka transakcji OVV
login.header = Login do przegl\u0105darki OVV
login.username = U\u017Cytkownik
login.password = Has\u0142o
login.submit = Login
login.invalidCredentials = Niew\u0142a\u015Bciwa nazwa u\u017Cytkownika i/lub has\u0142o.
login.loggedOut = Zosta\u0142e\u015B wylogowany.
login.sessionExpired = Twoja sesja wygas\u0142a. Zaloguj si\u0119 ponownie.

# Menu
menu.manualRedemption = Realizacja r\u0119czna
menu.transactions = Transakcje
menu.administration = Administacja
menu.administration.users = U\u017Cytkownicy
menu.app.version = Wersja
menu.batch = Batch- Plik
menu.batch.batchFileDetail = Szczeg\u00F3\u0142y pliku wsadowego
menu.batch.batchFileSummary = Podumowanie pliku wsadowego
menu.reports = Raporty
menu.reports.xlsReports = Raporty xls

# Common DataTables labels
datatables.noData = Nie znaleziono danych
datatables.show = _START_ - _END_
datatables.show.empty=Nie znaleziono danych
datatables.lengthMenu=Poka\u017C _MENU_ pozycje
datatables.paginate.next = Nast\u0119pny
datatables.paginate.previous = Poprzedni
datatables.processing = Wyszukiwanie . . .

# Common UI component labels
common.filter = Filtr
common.filter.search = Znajd\u017A
common.filter.reset = Usu\u0144 filtr
common.filter.ALL = Wszystko
common.filter.YES = Tak
common.filter.NO = Nie
common.filter.dateFrom=OVV Data od
common.filter.dateTo = OVV Data do
common.filter.createdFrom = Utworzone od
common.filter.createdTo = Utworzone do
common.filter.xlsExport = Export do excela
common.filter.expirationYear = Wygasa
common.filter.trainingMode = Transakcja treningowa

# Common errors
common.error.invalidRequest = Odrzucono nieprawid\u0142owe polecenie

# Forms
form.field.error.required = To pole jest wymagane
form.field.error.tooLong = Ilo\u015B\u0107 znak\u00F3w jest zbyt d\u0142uga.
form.field.error.lesser = Ilo\u015B\u0107 znak\u00F3w musi by\u0107 wi\u0119ksza ni\u017C{{fieldName}}.
form.field.error.invalidFormat = To pole ma nieprawid\u0142owy format.
form.number.error.notNumber = Warto\u015B\u0107 musi by\u0107 liczb\u0105.
form.number.error.invalid = Nieprawid\u0142owa liczba
form.number.error.gt = Warto\u015B\u0107 musi by\u0107 wi\u0119ksza ni\u017C{{min}}.
form.number.error.loe = Warto\u015B\u0107 musi by\u0107 mniejsza lub r\u00F3wna {{max}}.
form.password.error.tooShort = Has\u0142o musi zawiera\u0107 co najmniej 8 znak\u00F3w
form.password.error.digits = Has\u0142o musi zawiera\u0107 co najmniej 1 cyfr\u0119.
form.password.error.lowercase = Has\u0142o musi zawiera\u0107 co najmniej 1 ma\u0142\u0105 liter\u0119.
form.password.error.uppercase = Has\u0142o musi zawiera\u0107 co najmniej 1 du\u017C\u0105 liter\u0119.
form.password.error.match = Has\u0142a nie s\u0105 takie same.
form.field.error.dateIsInPast = Data jest z przesz\u0142o\u015Bci.
form.save = Zapisz zmiany
form.cancel = Anuluj
form.send = Wy\u015Blij

# Messages comopnent
comp.messages.info = Informacja:
comp.messages.success = Sukces:
comp.messages.warning = Ostrze\u017Cenie:
comp.messages.error = B\u0142\u0105d:

# Transaction List
transactions.list.heading = OVV Transakcje

# Administration \u2013 U\u017Cytkownicy
administration.users.list.heading =  U\u017Cytkownicy
administration.users.list.addUser = Dodaj u\u017Cytkownicy
administration.users.editUser = Edytuj u\u017Cytkownika
administration.users.changePassword = Zmie\u0144 has\u0142o
administration.users.disableUser = Wy\u0142\u0105cz u\u017Cytkownika
administration.users.enableUser = W\u0142\u0105cz u\u017Cytkownika
administration.users.add.heading = Dodaj u\u017Cytkownika
administration.users.add.title = Szczeg\u00F3\u0142y nowego u\u017Cytkownika
administration.users.edit.heading = Edytuj u\u017Cytkownika
administration.users.edit.title = Szczeg\u00F3\u0142y u\u017Cytkownika {{username}}
administration.users.info.created = Nowy u\u017Cytkownik zosta\u0142 pomy\u015Blnie utworzony
administration.users.info.updated = Dane u\u017Cytkownika zosta\u0142y pomy\u015Blnie zaktualizowane.
administration.users.error.usernameTaken = Wprowadzona nazwa u\u017Cytkownika jest ju\u017C u\u017Cywana , wprowad\u017A inn\u0105.
administration.users.enableUser.success = U\u017Cytkownik {{username}} zosta\u0142 w\u0142\u0105czony.
administration.users.disableUser.success = U\u017Cytkownik {{username}} zosta\u0142 wy\u0142\u0105czony.
administration.users.changePassword.success = Has\u0142o dla u\u017Cytkownika {{username}} zosta\u0142o pomy\u015Blnie zmienione.

# Transaction properties
transaction.serverDateTime = Data OVV, Czas
transaction.deviceDateTime = Data POS, Czas
transaction.countryCode = Kraj
transaction.deviceId = Kasa
transaction.stan = Nr trans.
transaction.paymentPlace = Sklep
transaction.amount = Kwota
transaction.type = Typ
transaction.voucherNumber = S/N- N/S
transaction.ovvIssuer = Emitent
transaction.offlineMode = Offline
transaction.resultCode = Wynik
transaction.trainingMode = Transakcja treningowa
transaction.store = Sklep
transaction.id = ID
transaction.detail = Szczeg\u00F3\u0142y
transaction.validationHost = Typ serwera
transaction.partnerId = Partner
transaction.errorDescription = Dodatkowe informacje
transaction.dateOrderInvalid = Data od jest p\u00F3\u017Cniejsza ni\u017C Data do
transaction.intervalToBig = Maksymalny czas wyszukiwania transakcji to {{interval}} dni
transaction.intervalDateInFuture = Data nie mmo\u017Ce by\u0107 w przysz\u0142o\u015Bci
transaction.manualRedemption = R\u0119czna realizacja

# User properties
user.username = Nazwa u\u017Cytkownika
user.name = Nazwa
user.firstName = Imi\u0119
user.lastName = Nazwisko
user.email = e-mail
user.role = Rola
user.active = Aktywny
user.password = Has\u0142o
user.passwordConfirm = Potwierd\u017A has\u0142o
user.store = Sklep
user.countryCode = Kraj

#
# Code lists
#

# Transaction Types
transaction.type.VALIDATION = Zatwierdzone
transaction.type.CHECK = Zatwierdzone
transaction.type.REVERSAL = Anulowane

# Result Codes
transaction.resultCode.APPROVED = Zaakceptowane
transaction.resultCode.ALREADY_USED = Ju\u017C u\u017Cyty
transaction.resultCode.MESSAGE_FORMAT_ERROR = B\u0142\u0105d formatu
transaction.resultCode.WRONG_REVERSAL = B\u0142\u0119dna anulacja
transaction.resultCode.VOUCHER_NOT_FOUND = Nie znaleziono
transaction.resultCode.ISSUER_NOT_FOUND = Nieznany emitent
transaction.resultCode.TIMEOUT = Przekroczony limit czasu
transaction.resultCode.WRONG_REPEATED_REQUEST = B\u0142\u0119dne ponowne polecenie
transaction.resultCode.WRONG_VOUCHER_STATE = B\u0142\u0119dny status bonu
transaction.resultCode.WRONG_STORE = B\u0142\u0119dny sklep
transaction.resultCode.DECLINED=Zablokowane przez emitenta
transaction.resultCode.CANNOT_PROCESS = Nie przeprocesowane
transaction.resultCode.SCAN_AGAIN  = Zeskanuj ponownie
transaction.resultCode.STOLEN = Skradzione
transaction.resultCode.ALREADY_USED_SAME_TRN = Ju\u017C u\u017Cyte w tej samej tr.
transaction.resultCode.AFTER_VALIDITY = Przeterminowane
transaction.resultCode.AMOUNT_DEF_NOT_FOUND=Nie znaleziono kwoty
transaction.resultCode.NOT_ACCEPTED=Nie zaakceptowany
# User Roles
user.role.ADMIN = Administrator
user.role.VIEWER = Podgl\u0105d
user.role.MANUAL_REDEMPTION = Realizacja
user.role.BATCH = Plik
user.role.STORE_MANAGER = Manager

# Countries
country.PL = PL
country.CZ = CZ
country.SK = SK
country.HU = HU

#Manual Redemption
redemption.heading = R\u0119czna realizacja bonu
redemption.transactionType = Typ transakcji
redemption.voucherNumber = Numer bonu
redemption.amount = Kwota
redemption.store = Sklep
redemption.device=Kasa
redemption.stan=Nr trans.
redemption.validation.APPROVED = Bon zosta\u0142 pomy\u015Blnie zaakceptowany.
redemption.validation.ALREADY_USED = Bon zosta\u0142 ju\u017C u\u017Cyty.
redemption.validation.MESSAGE_FORMAT_ERROR = B\u0142\u0119dny format bonu. Skontaktuj si\u0119 z administratorem.
redemption.validation.WRONG_REVERSAL = Status bonu nie mo\u017Ce by\u0107 zmieniony.
redemption.validation.VOUCHER_NOT_FOUND = Nie znaleziono bonu.
redemption.validation.ISSUER_NOT_FOUND = Nie znaleziono emitenta.
redemption.validation.TIMEOUT = Przekroczono limit czasu. Spr\u00F3buj ponownie.
redemption.validation.WRONG_REPEATED_REQUEST = B\u0142\u0119dne ponowne polecenie. Skontaktuj si\u0119 z administratorem.
redemption.validation.WRONG_VOUCHER_STATE = Bon zosta\u0142 pomy\u015Blnie zaakceptowany.
redemption.validation.WRONG_STORE = Wybrano b\u0142\u0119dny sklep. Wybierz sklep ponownie.
redemption.validation.DECLINED=Zablokowane przez emitenta.
redemption.validation.CANNOT_PROCESS = Nie mo\u017Cna przeprocesowa\u0107 realizacji. Skontaktuj si\u0119 z administratorem.
redemption.validation.SCAN_AGAIN  = Zeskanuj ponownie.
redemption.validation.STOLEN = Skradzione.
redemption.validation.ALREADY_USED_SAME_TRN = Ju\u017C u\u017Cyte w tej samej tr.
redemption.validation.AFTER_VALIDITY = Przeterminowane.
redemption.validation.AMOUNT_DEF_NOT_FOUND = Warto\u015B\u0107 bonu nie mo\u017Ce zosta\u0107 okre\u015Blona automatycznie.
redemption.validation.NOT_ACCEPTED=Nie zaakceptowany.

redemption.redemptionDateTime=Data
redemption.setAmount.heading=Ustaw warto\u015B\u0107 bonu
redemption.setAmount.info=Warto\u015B\u0107 bonu nie mo\u017Ce zosta\u0107 okre\u015Blona automatycznie. Wprowad\u017A warto\u015B\u0107 r\u0119cznie.
redemption.confirmAmount=Potwierd\u017A


# Export
export.failed = Export nie powi\u00F3d\u0142 si\u0119.
export.tooBig = Exportowane dane s\u0105 zbyt du\u017Ce, maksymalna ilo\u015B\u0107 transakcji nie mo\u017Ce przekracza\u0107 65000 linii. U\u017Cyj filtr\u00F3w i spr\u00F3buj ponownie.
batch.detail.heading = Szczeg\u00F3\u0142y pliku OVV
batch.summary.heading = Podsumowanie pliku OVV
batch.resultDescription = Opis wyniku
batch.created = Utworzono
batch.fileName = Nazwa pliku
batch.action = Akcja
batch.detail = Szczeg\u00F3\u0142y
batch.reconciliated = Rozliczono
batch.itemsCount = Ilo\u015B\u0107 element\u00F3w
batch.summary.detail = Szczeg\u00F3\u0142y pliku
batch.resultCode.0 = bon jest potwierdzony przez emitenta
batch.resultCode.1 = bon nie zosta\u0142 potwierdzony przez emitenta i wymaga sprawdzenia. Emitent mo\u017Ce za\u017C\u0105da\u0107 dostarczenia bonu przez Billa.
batch.resultCode.2 = bon nie zosta\u0142 potwierdzony przez emitenta, wymagane jest oficjalne dochodzenie ( przez policj\u0119 lub inny organ publiczny). Billa ma obowi\u0105zek dostarczy\u0107 bon do emitenta.
batch.resultCode.3 = bon zosta\u0142 odrzucony przez emitenta poniewa\u017C zosta\u0142 ju\u017C zrealizowany przez innego partnera.
batch.resultCode.4 = bon zosta\u0142 odrzucony przez emitenta. Bon mo\u017Ce by\u0107 fa\u0142szywy ( nie istniej\u0105cy lub b\u0142\u0119dny kod).
batch.resultCode.5 = bon nie posiada w\u0142a\u015Bciwego kodu lokalizacji i/lub centrum kosztowego. Proces przetwarzania zostanie zatrzymany do momentu rozwi\u0105zania problemu.
batch.detail.title = Szczeg\u00F3\u0142y transakcji
batch.detail.close = Zamknij
batch.detail.notExistTransaction = Informacja o transakcji z ID "{{id}}" nie jest jeszcze dost\u0119pna, spr\u00F3buj ponownie p\u00F3\u017Aniej.
batch.costCentreSiteCode = Centrum koszt\u00F3w/Numer sklepu

# Change password
users.changePassword.heading = Zmiana has\u0142a
users.changePassword.title = Wprowad\u017A nowe has\u0142o dla u\u017Cytkownika {{username}}.
users.changePassword.oldPassword = Stare has\u0142o
users.changePassword.newPassword = Nowe has\u0142o
users.changePassword.confirmNewPassword = Potwierd\u017A nowe has\u0142o
changePassword.submit = Zmiana has\u0142a

oldPassword.empty.passwordChangeForm.oldPassword = Pole \u201CStare has\u0142o\u201D jest puste
newPassword.empty.passwordChangeForm.newPassword = Pole \u201CNowe has\u0142o\u201D jest puste
newPasswordConfirm.empty.passwordChangeForm.newPasswordConfirm = Pole \u201CNowe potwierdzone has\u0142o\u201D jest puste
newPassword.noMatch.passwordChangeForm.newPassword = Wprowadzone has\u0142a nie s\u0105 takie same
newPassword.sameWithOld.passwordChangeForm.newPassword = Stare i nowe has\u0142o s\u0105 takie same
password.tooWeak.passwordChangeForm = Nowe has\u0142o musi si\u0119 sk\u0142ada\u0107 z co najmniej 8 znak\u00F3w (z jednej cyfry, jednej ma\u0142ej i jednej du\u017Cej litery)
object.invalid.passwordChangeForm = Stare has\u0142o nie jest poprawne

common.cancel = Anuluj

transaction.category = Kategoria
transaction.category.MEAL = Kupon na jedzenie
transaction.category.SOCIAL = Kupon socjalny
transaction.category.GIFT = Kupon na prezent
transaction.category.SCHOOL = Kupon szkolny
transaction.category.CHILDREN = Kupon dla dzieci
transaction.category.OTHER = Inny


# Reports
reports.list.heading = Raporty XLS
reports.list.createReport = Nowy raport
reports.list.listTitle = Raporty -
reports.list.downloadReport = Pobierz raport

reports.new.heading = Nowy raport
reports.new.title = W\u0142a\u015Bciwo\u015Bci nowego raportu
reports.new.parameters = Parametry wej\u015Bciowe
reports.new.info.success = Nowy raport zosta\u0142 przes\u0142any do przetworzenia

# Report properties
report.type = Rodzaj raportu
report.status = Status przyjmuj\u0105cego sklepu
report.store = Sklep utworzenia
report.createdDate = Data utworzenia
report.params.dateFrom = Data od
report.params.dateTo = Data do
report.params.store = Sklep

report.type.USED_VOUCHERS = U\u017Cywane kupony
report.type.CATEGORY=Raport kategorii

# Report statuses
report.status.QUEUED = Czeka na przetworznie
report.status.IN_PROGRESS = W trakcie
report.status.OK = Zako\u0144czono
report.status.FAILED = B\u0142\u0105d
report.status.LARGE_EXPORT = Przekroczono limit

reports.dateInterval.invalid = Data od jest p\u00F3\u017Cniejsza ni\u017C Data do
reports.dateInterval.tooBig = Przekroczono maksymalny interwa\u0142

