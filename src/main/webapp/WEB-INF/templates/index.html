<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" ng-app="ovv-viewer">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title th:text="#{app.title}">OVV Viewer</title>
    <link rel="stylesheet" th:href="@{/resources/vendor/sing/styles/vendor-d96f386b99.css}">
    <link rel="stylesheet" th:href="@{/resources/vendor/sing/styles/app-bbd0b7a4b2.css}">
    <link rel="stylesheet" th:href="@{/resources/css/main.css}">
    <link rel="stylesheet" th:href="@{/resources/css/dialog.css}">
    <base th:href="@{/}">
</head>
<body ng-controller="AppController"
    ng-class="{'nav-static': app.state['nav-static'], 'login-page': $state.is('login'), 'error-page': $state.is('error')}">
    <!--[if lt IE 10]>
<p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve your experience.</p>
<![endif]-->
    <div class="app" id="app" data-ui-view cg-busy="busy"></div>
    <script th:src="@{/resources/vendor/sing/scripts/vendor-2e475ba205.js}" data-pace-options='{ "target": ".content-wrap", "ghostTime": 1000 }'></script>

    <script th:src="@{/app/modules/_sing/core/core.module.js}"></script>
    <script th:src="@{/app/modules/_sing/core/widget/widget.js}"></script>
    <script th:src="@{/app/modules/_sing/core/utils/utils.js}"></script>
    <script th:src="@{/app/modules/_sing/core/navigation/navigation.js}"></script>
    <script th:src="@{/app/modules/_sing/core/core.js}"></script>
    <script th:src="@{/app/modules/_sing/core/config.js}"></script>

    <script th:src="@{/app/components/input-errors/input-errors.js}"></script>,
    <script th:src="@{/app/components/messages/messages.js}"></script>

    <script th:src="@{/app/modules/_sing/core/locale/angular-locale_cs.js}"></script>
    <script th:src="@{/app/modules/_sing/core/locale/angular-locale_hu.js}"></script>
    <script th:src="@{/app/modules/_sing/core/locale/angular-locale_sk.js}"></script>
    <script th:src="@{/app/modules/_sing/core/locale/angular-locale_pl.js}"></script>

    <script th:src="@{/app/common/config-service.js}"></script>
    <script th:src="@{/app/common/utils.js}"></script>
    <script th:src="@{/app/modules/common/tables/tables.module.js}"></script>
    <script th:src="@{/app/modules/common/tables/table-messages.js}"></script>
    <script th:src="@{/app/modules/common/tables/table-ajax.js}"></script>
    <script th:src="@{/app/modules/common/validation/validation.module.js}"></script>
    <script th:src="@{/app/modules/common/validation/validation-errors.js}"></script>
    <script th:src="@{/app/modules/common/validation/match.js}"></script>
    <script th:src="@{/app/modules/common/cashRegister-resource.js}"></script>

    <script th:src="@{/app/modules/transactions/transactions.module.js}"></script>
    <script th:src="@{/app/modules/transactions/transaction-list-controller.js}"></script>
    <script th:src="@{/app/modules/transactions/transaction-detail-controller.js}"></script>
    <script th:src="@{/app/modules/transactions/transaction-resource.js}"></script>
    <script th:src="@{/app/modules/users/users.module.js}"></script>
    <script th:src="@{/app/modules/users/user-resource.js}"></script>
    <script th:src="@{/app/modules/users/user-list-controller.js}"></script>
    <script th:src="@{/app/modules/users/user-new-controller.js}"></script>
    <script th:src="@{/app/modules/users/user-edit-controller.js}"></script>
    <script th:src="@{/app/modules/users/user-password-change.js}"></script>
    <script th:src="@{/app/modules/stores/store.module.js}"></script>
    <script th:src="@{/app/modules/stores/store-resource.js}"></script>
    <script th:src="@{/app/modules/redemption/redemption.module.js}"></script>
    <script th:src="@{/app/modules/redemption/redemption-controller.js}"></script>
    <script th:src="@{/app/modules/redemption/redemption-resource.js}"></script>
    <script th:src="@{/app/modules/batch/batch.module.js}"></script>
    <script th:src="@{/app/modules/batch/batch-detail-controller.js}"></script>
    <script th:src="@{/app/modules/batch/batch-summary-controller.js}"></script>
    <script th:src="@{/app/modules/batch/batch-resource.js}"></script>
    <script th:src="@{/app/modules/reports/reports.module.js}"></script>
    <script th:src="@{/app/modules/reports/report-list-controller.js}"></script>
    <script th:src="@{/app/modules/reports/report-new-controller.js}"></script>
    <script th:src="@{/app/modules/reports/report-resource.js}"></script>
    <script th:src="@{/app/index.js}"></script>
</body>
</html>
