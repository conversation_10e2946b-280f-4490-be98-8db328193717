<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title th:text="#{app.title}">OVV Viewer</title>
    <link rel="stylesheet" th:href="@{/resources/vendor/sing/styles/vendor-d96f386b99.css}">
    <link rel="stylesheet" th:href="@{/resources/vendor/sing/styles/app-bbd0b7a4b2.css}">
</head>

<body class="error-page">
    <div class="container">
        <main id="content" class="error-container" role="main">
        <div class="row">
            <div class="col-lg-4 col-sm-6 col-xs-10 col-lg-offset-4 col-sm-offset-3 col-xs-offset-1">
                <div class="error-container">
                    <h1 class="error-code" th:text="${errorCode}">404</h1>
                    <p class="error-info" th:text="#{${errorInfoMsgKey}}">Error info message</p>
                    <p class="error-help mb" th:text="#{${errorHelpMsgKey}}">Error help message</p>
                    <a class="btn btn-inverse" th:href="@{/}">
                        <i class="fa fa-home text-warning mr-xs"></i>
                        <span th:text="#{error.goHome}">Home</span>
                    </a>
                </div>
            </div>
        </div>
        </main>
    </div>
</body>
