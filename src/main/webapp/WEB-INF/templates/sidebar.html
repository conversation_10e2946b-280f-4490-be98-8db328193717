<nav id="sidebar" role="navigation" class="sidebar" >
    <div class="js-sidebar-content">
        <header class="logo hidden-xs">
            <a class="full-app-title" href="index.html" th:text="#{app.fullName}">APP full</a>
            <div class="full-app-title fs-mini text-gray" th:text="${appSubtitle}">Subtitle</div>
            <a class="short-app-title" href="index.html" th:text="#{app.shortName}">APP</a>
        </header>
        <div class="sidebar-status visible-xs">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                <strong th:text="${loggedUsername}">Logged User</strong>
                <b class="caret"></b>
            </a>
            <ul class="dropdown-menu">
                <li><a href="#"><i class="glyphicon glyphicon-user"></i> &nbsp; <span th:text="#{navbar.myAccount}">My Account</span></a></li>
                <li class="divider"></li>
                <li><a th:href="@{/logout}"><i class="fa fa-sign-out"></i> &nbsp; <span th:text="#{navbar.logout}">Log Out</span></a></li>
            </ul>
        </div>
        <!-- Main menu items -->
        <ul id="sidebar-menu-list" class="sidebar-nav">
            <li>
                <a data-ui-sref="app.transactions.list" data-ui-sref-active="active-menu-link">
                    <span class="icon"><i class="fa fa-database"></i></span>
                    <span th:text="#{menu.transactions}">Transactions</span>
                </a>
            </li>
<!--            <li sec:authorize="hasRole('STORE_MANAGER') || hasRole('MANUAL_REDEMPTION') || hasRole('ADMIN')">-->
<!--                <a data-ui-sref="app.redemption.create" data-ui-sref-active="active-menu-link">-->
<!--                    <span class="icon"><i class="fa fa-money"></i></span>-->
<!--                    <span th:text="#{menu.manualRedemption}">Manual Redemption</span>-->
<!--                </a>-->
<!--            </li>-->
            <li sec:authorize="hasRole('STORE_MANAGER') || hasRole('BATCH') || hasRole('ADMIN')">
                <a class="collapsed" data-target="#batch-menu" data-toggle="collapse" data-parent="#sidebar-menu-list">
                    <span class="icon"><i class="fa fa-suitcase"></i></span>
                    <span th:text="#{menu.batch}" style="padding-right: 13px">Batch</span>
                    <i class="toggle fa fa-angle-down"></i>
                </a>
                <ul id="batch-menu" class="collapse">
                    <li>
                        <a data-ui-sref="app.batch.summary" data-ui-sref-active="active-menu-link">
                            <i class="fa fa-files-o"></i>
                            <span th:text="#{menu.batch.batchFileSummary}">Batch File Summary</span>
                        </a>
                    </li>
                    <li>
                        <a data-ui-sref="app.batch.detail" data-ui-sref-active="active-menu-link">
                            <i class="fa fa-file-o"></i>
                            <span th:text="#{menu.batch.batchFileDetail}">Batch File Detail</span>
                        </a>
                    </li>
                </ul>
            </li>
            <li>
                <a class="collapsed" data-target="#reports-menu" data-toggle="collapse" data-parent="#sidebar-menu-list">
                    <span class="icon"><i class="fa fa-database"></i></span>
                    <span th:text="#{menu.reports}" style="padding-right: 13px">Reports</span>
                    <i class="toggle fa fa-angle-down"></i>
                </a>
                <ul id="reports-menu" class="collapse">
                    <li>
                        <a data-ui-sref="app.reports.list" data-ui-sref-active="active-menu-link">
                            <i class="fa fa-fw fa-table"></i>
                            <span th:text="#{menu.reports.xlsReports}">XLS reports</span>
                        </a>
                    </li>
                </ul>
            </li>
            <li sec:authorize="hasRole('ADMIN') || hasRole('STORE_MANAGER')">
                <a class="collapsed" data-target="#admin-menu" data-toggle="collapse" data-parent="#sidebar-menu-list">
                    <span class="icon"><i class="fa fa-sliders"></i></span>
                    <span th:text="#{menu.administration}">Admin</span>
                    <i class="toggle fa fa-angle-down"></i>
                </a>
                <ul id="admin-menu" class="collapse">
                    <li>
                        <a data-ui-sref="app.users.list" data-ui-sref-active="active-menu-link">
                            <i class="fa fa-fw fa-users"></i>
                            <span th:text="#{menu.administration.users}">Users</span>
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
        <span id="app-version" class="fs-mini text-gray"><span th:text="#{menu.app.version}">Version</span>: <span th:text="${appVersion}">unknown</span></span>
    </div>
</nav>
