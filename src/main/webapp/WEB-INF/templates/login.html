<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title th:text="#{app.title}">OVV Viewer</title>
    <link rel="stylesheet" th:href="@{/resources/vendor/sing/styles/vendor-d96f386b99.css}">
    <link rel="stylesheet" th:href="@{/resources/vendor/sing/styles/app-bbd0b7a4b2.css}">
</head>

<body class="login-page">
    <div class="container">
        <main id="content" class="widget-login-container" role="main">
        <div class="row">
            <div class="col-lg-4 col-sm-6 col-xs-10 col-lg-offset-4 col-sm-offset-3 col-xs-offset-1">
                <h4 class="widget-login-logo animated fadeInUp">
                    <i class="fa fa-circle text-gray"></i><span th:text="#{login.title}">OVV Viewer</span><i class="fa fa-circle text-warning"></i>
                </h4>
                <section class="widget widget-login animated fadeInUp">
                    <header>
                        <h3 class="text-align-center" th:text="#{login.header}">Login to OVV Viewer</h3>
                    </header>
                    <div class="widget-body">

                        <div class="alert alert-sm alert-danger" th:if="${param.error}" th:text="#{login.invalidCredentials}">
                            Wrong credentials
                        </div>
                        <div class="alert alert-sm alert-success" th:if="${param.logout}" th:text="#{login.loggedOut}">
                            Logged out
                        </div>
                        <div class="alert alert-sm alert-danger" th:if="${param.expired}" th:text="#{login.sessionExpired}">
                            Session expired
                        </div>

                        <form method="post" th:action="@{/login}" class="login-form mt-lg">
                            <input type="hidden" name="urlFragment" id="urlFragment"/>
                            <div class="form-group">
                                <input type="text" class="form-control" name="username" placeholder="User" autofocus
                                        th:attr="placeholder=#{login.username}" autocomplete="off">
                            </div>
                            <div class="form-group">
                                <input type="password" class="form-control" name="password" placeholder="Pwd"
                                        th:attr="placeholder=#{login.password}" autocomplete="off">
                            </div>
                            <div class="clearfix text-align-center">
                                <button type="submit" class="btn btn-inverse btn-sm width-100">
                                    <i class="fa fa-sign-in"></i>
                                    <span th:text="#{login.submit}">Login</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </section>
            </div>
        </div>
        </main>
        <footer class="page-footer"><span th:text="${currentYear}">2017</span> &copy;, Diebold Nixdorf</footer>
    </div>
</body>
</html>
