<div class="container-fluid">
    <!-- .navbar-header contains links seen on xs & sm screens -->
    <div class="navbar-header">
        <ul class="nav navbar-nav">
            <li>
                <!-- whether to automatically collapse sidebar on mouseleave. If activated acts more like usual admin templates -->
                <a class="hidden-sm hidden-xs" href="#" data-sn-action="toggle-navigation-state"
                        title="Turn on/off sidebar collapsing" data-placement="bottom" data-tooltip>
                    <i class="fa fa-bars fa-lg"></i>
                </a>
                <!-- shown on xs & sm screen. collapses and expands navigation -->
                <a class="visible-sm visible-xs" data-sn-action="toggle-navigation-collapse-state"
                        href="#" title="Show/hide sidebar" data-placement="bottom" data-tooltip>
                    <span class="rounded rounded-lg bg-gray text-white visible-xs">
                        <i class="fa fa-bars fa-lg"></i>
                    </span>
                    <i class="fa fa-bars fa-lg hidden-xs"></i>
                </a>
            </li>
            <li class="ml-sm mr-n-xs hidden-xs">
                <a class="faa-parent animated-hover" ng-click="$state.reload()">
                    <i class="fa fa-refresh fa-lg faa-spin"></i>
                </a>
            </li>
        </ul>
        <!-- xs & sm screen logo -->
        <a class="navbar-brand visible-xs" href="index.html">
            <i class="fa fa-circle text-gray mr-n-sm"></i>
            <i class="fa fa-circle text-warning"></i>
            &nbsp; <span th:text="#{app.name}">App Name</span> &nbsp;
            <i class="fa fa-circle text-warning mr-n-sm"></i>
            <i class="fa fa-circle text-gray"></i>
        </a>
    </div>

    <!-- this part is hidden for xs screens -->
    <div class="collapse navbar-collapse">
        <ul class="nav navbar-nav navbar-right">
            <li class="dropdown" data-dropdown>
                <a class="dropdown-toggle" data-dropdown-toggle>
                    <strong th:text="${loggedUsername}">Logged User</strong>
                    &nbsp; <b class="caret"></b>
                </a>
                <ul class="dropdown-menu">
                    <!-- TODO
                    <li><a ui-sref="app.users.myAccount"><i class="glyphicon glyphicon-user"></i> &nbsp; <span th:text="#{navbar.myAccount}">My Account</span></a></li>
                    <li class="divider"></li>
                     -->
                    <li><a href="#" data-ng-click="passwordChange()"><i class="glyphicon glyphicon-lock"></i> &nbsp; <span th:text="#{navbar.changePassword}">Change Password</span></a></li>
                    <li><a th:href="@{/logout}"><i class="fa fa-sign-out"></i> &nbsp; <span th:text="#{navbar.logout}">Log Out</span></a></li>
                </ul>
            </li>
        </ul>
    </div>
</div>
