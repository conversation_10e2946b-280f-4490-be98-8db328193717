<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title th:text="#{app.title}">OVV Viewer</title>
    <link rel="stylesheet" th:href="@{/resources/vendor/sing/styles/vendor-d96f386b99.css}">
    <link rel="stylesheet" th:href="@{/resources/vendor/sing/styles/app-bbd0b7a4b2.css}">
</head>

<body class="login-page">
    <div class="container">
        <main id="content" class="widget-login-container" role="main">
        <div class="row">
            <div class="col-lg-4 col-sm-6 col-xs-10 col-lg-offset-4 col-sm-offset-3 col-xs-offset-1">
                <h4 class="widget-login-logo animated fadeInUp">
                    <i class="fa fa-circle text-gray"></i><span th:text="#{login.title}">OVV Viewer</span><i class="fa fa-circle text-warning"></i>
                </h4>
                <section class="widget widget-login animated fadeInUp">
                    <header>
                        <h3 class="text-align-center" th:text="#{users.changePassword.heading}">Change password</h3>
                    </header>
                    <div class="widget-body">
                        <form method="post" th:action="@{/changePassword/process}" th:object="${passwordChangeForm}" class="login-form mt-lg">
                            <input type="hidden" name="userName" id="userName" th:value="${userName}"/>
                            <input type="hidden" name="userId" id="userId" th:value="${userId}"/>
                            <div class="alert alert-danger" th:if="${#fields.hasErrors('*')}">
                                <p th:each="err : ${#fields.errors('*')}" th:text="${err}"></p>
                            </div>
                            <div class="form-group">
                                <input type="text" class="form-control" name="userName" autofocus
                                        autocomplete="off" th:value="${userName}"
                                        disabled="disabled">
                            </div>
                            <div class="form-group">
                                <input type="password" class="form-control" name="oldPassword" placeholder="Pwd"
                                        th:attr="placeholder=#{users.changePassword.oldPassword}" autocomplete="off">
                            </div>
                            <div class="form-group">
                                <input type="password" class="form-control" name="newPassword" placeholder="Pwd"
                                       th:attr="placeholder=#{users.changePassword.newPassword}" autocomplete="off">
                            </div>
                            <div class="form-group">
                                <input type="password" class="form-control" name="newPasswordConfirm" placeholder="Pwd"
                                       th:attr="placeholder=#{users.changePassword.confirmNewPassword}" autocomplete="off">
                            </div>
                            <div class="clearfix text-align-center">
                                <button type="submit" class="btn btn-inverse btn-sm width-200">
                                    <i class="fa fa-sign-in"></i>
                                    <span th:text="#{changePassword.submit}">Login</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </section>
            </div>
        </div>
        </main>
        <footer class="page-footer"><span th:text="${currentYear}">2017</span> &copy;, Diebold Nixdorf</footer>
    </div>
</body>
</html>

