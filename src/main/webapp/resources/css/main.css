body {
    width: calc(100vw - 17px);
}

.content {
    padding: 85px 10px 20px 20px;
}

/**
 * Sidebar app title.
 */
.js-sidebar-content header {
    line-height: normal;
    padding-top: 20px;
}
.js-sidebar-content header {
    white-space: nowrap;
}
.js-sidebar-content header .full-app-title {
    display: inherit;
}
.js-sidebar-content header .short-app-title {
    display: none;
}
.nav-collapsed .js-sidebar-content header .full-app-title {
    display: none;
}
.nav-collapsed .js-sidebar-content header .short-app-title {
    display: inherit;
}

input.ng-invalid.ng-dirty, form.ng-submitted input.ng-invalid,
        textarea.ng-invalid.ng-dirty, form.ng-submitted textarea.ng-invalid,
        div.select2-container.ng-invalid.ng-dirty a.select2-choice,
        form.ng-submitted div.select2-container.ng-invalid a.select2-choice {
    border-color: #dd5826;
    box-shadow: 0 1px 1px rgba(221, 88, 38, 0.075) inset;
}

/*fix issue with 10 px width onclick area on select box input, when select box is rendered disabled(ui-select bug)*/
input.ui-select-search {
width: 100% !important;
}

div.vm-input-errors {
    font-size: 12px;
    padding-left: 0;
}
div.vm-input-errors span {
    color: #dd5826;
    display: block;
}

.table > tbody > tr > td {
    vertical-align: middle;
}

.table-clickable > tbody > tr {
    cursor: pointer;
}

.bg-danger > tbody > tr:hover {
    color : #dd5826;
}

/**
 * Bottom alignment of a column content in a row.
 */
.vm-row {
    font-size: 0;
}
.vm-row > * {
    float: none;
    display: inline-block;
    font-size: 14px;
}
.vm-row .bottom {
    vertical-align: bottom;
}

/**
 * Buttons in table rows, visible only in hovered rows.
 */
table div.vm-table-row-last {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.vm-table-row-buttons, .vm-table-row-buttons .vm-actions, .vm-table-row-buttons .vm-ellipsis {
    float: right;
    display: inline-block;
}
.table-hover > tbody > tr:hover .vm-table-row-buttons .vm-ellipsis {
    display: none;
}
.vm-table-row-buttons .vm-actions {
    visibility: hidden;
    opacity: 0;
    height: 0;
    overflow: hidden;
    /*display: none;*/
}
.table-hover > tbody > tr:hover .vm-table-row-buttons .vm-actions {
    visibility: visible;
    opacity: 1;
    height: auto;
    /*display: inline-block;*/
    transition: opacity 0.4s linear;
}
.vm-table-row-buttons .vm-actions > a {
    margin-left: 0.5em;
    color: #555;
}
.vm-table-row-buttons .vm-actions > a:focus, .vm-table-row-buttons .vm-actions > a:hover {
    color: #222;
}

td.slider-cell {
    width: 300px;
    padding-left: 20px !important;
    padding-right: 20px !important;
}
td.slider-cell .slider.slider-horizontal .slider-handle {
    margin-top: -12px;
}

/**
 * Filters.
 */
.vm-filter {
    margin-bottom: 15px;
}
.vm-filter .form-group {
    margin-bottom: 5px;
}
.vm-simple-filter {
    margin-bottom: 10px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e5e5;
}
.vm-filter label, .vm-simple-filter label {
    margin-bottom: 0px;
    font-size: 13px;
}
.vm-filter .btn-toolbar, .vm-simple-filter .btn-toolbar {
    margin-bottom: 0px;
}
.vm-filter .btn-toolbar .btn, .vm-simple-filter .btn-toolbar .btn {
    padding: 5px 30px;
}

.vm-pointer {
    cursor: pointer;
}

/**
 * Rows with custom margins.
 */
.vm-tall-row {
    padding-bottom: 10px;
}

table tr.empty-table-info td {
    text-align: center;
    font-style: italic;
}

table.dataTable tbody, table.dataTable thead {
    font-size: 12px;
}

/**
 * Global error dialog.
 */
.vm-error-container {
    text-align: center;
}
.vm-error-container h2 {
    font-weight: 400;
    color: #F00;
}

/**
 * App version
 */
.js-sidebar-content #app-version {
    position: absolute;
    bottom: 0;
    padding: 1em;
    text-align: center;
    width: 100%;
}
.nav-collapsed .js-sidebar-content #app-version {
    display: none;
}

/**
 * Fixed overflow of tabs.
 */
.vm-tabs .tab-content, .form-wizard .tab-content {
    overflow: visible;
}

/**
 * Table layouts.
 */
.table-container {
    display: table;
    width: 100%;
}
.table-col {
    display: table-cell;
    vertical-align: middle;
}
.padding-10 {
    padding: 10px;
}

