(function() {
    'use strict';

    /**
     * Module for transactions viewing.
     */
    angular.module('ovv.batch', ['ovv.common.tables'])
        .config(batchConfig);

    /* @ngInject */
    function batchConfig($stateProvider) {
        $stateProvider
            .state('app.batch', {
                abstract: true,
                url: '/batch',
                template: '<ui-view/>'
            })
            .state('app.batch.detail', {
                url: '/detail',
                params: {
                    fileName: null
                },
                templateUrl: 'app/modules/batch/batch-detail.html',
                controller: 'BatchDetailController'
            })
            .state('app.batch.summary', {
                url: '/',
                templateUrl: 'app/modules/batch/batch-summary.html',
                controller: 'BatchSummaryController'
            });
    }
})();
