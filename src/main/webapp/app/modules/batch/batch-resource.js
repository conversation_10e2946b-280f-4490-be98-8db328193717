(function() {
    'use strict';

    angular.module('ovv.batch')
        .factory('BatchResource', BatchResource);

    /* @ngInject */
    function BatchResource($resource) {
        var url = './api/batch';

        return $resource(url + '/', {}, {
            send: { url: url + '/', method: 'POST' },
            findFileName: { url: url + '/find', method: 'GET', isArray:true }
        });

    }
})();
