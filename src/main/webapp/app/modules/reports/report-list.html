<h1 class="page-title" translate>reports.list.heading</h1>

<vm-messages messages="messages"></vm-messages>

<div class="btn-toolbar">
    <a class="btn btn-primary btn-sm width-150" data-ui-sref="app.reports.new">
        <i class="fa fa-plus">&nbsp;</i>
        <span translate>reports.list.createReport</span>
    </a>
</div>

<vm-section class="vm-filter">
    <vm-section-title translate>common.filter</vm-section-title>
    <vm-section-body>
        <form>
            <div class="row">
                <div class="col-sm-4 form-group">
                    <label for="type" translate>report.type</label>
                    <ui-select id="type" name="type" class="form-control"
                            ng-model="filter.type">
                        <ui-select-match allow-clear="true">
                            {{$select.selected.title | translate}}
                        </ui-select-match>
                        <ui-select-choices repeat="type in reportTypes">
                            <div ng-bind-html="type.title | translate"></div>
                        </ui-select-choices>
                    </ui-select>
                </div>
                <div class="col-sm-3 form-group">
                    <label for="status" translate>report.status</label>
                    <ui-select id="status" name="status" class="form-control"
                            ng-model="filter.status">
                        <ui-select-match allow-clear="true">
                            {{$select.selected.title | translate}}
                        </ui-select-match>
                        <ui-select-choices repeat="status in reportStatuses">
                            <div ng-bind-html="status.title | translate"></div>
                        </ui-select-choices>
                    </ui-select>
                </div>
                <div class="col-sm-3 form-group">
                    <label for="dateFrom" translate>common.filter.createdFrom</label>
                        <input type="text" datetimepicker datetimepicker-options="{{datetimepickerOptions}}"
                               id="dateFrom" name="dateFrom"
                               class="form-control" ng-model="filter.dateFrom" />
                </div>
                <div class="col-sm-3 form-group">
                    <label for="dateTo" translate>common.filter.createdTo</label>
                        <input type="text" datetimepicker datetimepicker-options="{{datetimepickerOptions}}"
                               id="dateTo" name="dateTo"
                               class="form-control" ng-model="filter.dateTo" />
                </div>
                <div class=" col-sm-4 form-group">
                    <label for="store" translate>report.store</label>
                        <ui-select id="store" name="store" class="form-control"
                                   ng-model="filter.store" search-enabled="true">
                            <ui-select-match allow-clear="true">
                                {{$select.selected.siteCode + ' ' + $select.selected.name}}
                            </ui-select-match>
                            <ui-select-choices repeat="store in stores track by $index"
                                               refresh="refreshStores($select.search)"
                                               refresh-delay="2">
                                {{store.siteCode + ' ' + store.name}}
                            </ui-select-choices>
                        </ui-select>
                </div>
            </div>
            <div class="btn-toolbar">
                <button type="submit" class="btn btn-sm btn-primary" ng-click="searchReports()">
                    <i class="fa fa-search mr-xs"></i>
                    <span translate>common.filter.search</span>
                </button>
                <button type="reset" class="btn btn-sm btn-gray" ng-click="clearFilter()">
                    <i class="fa fa-times text-danger mr-xs"></i>
                    <span translate>common.filter.reset</span>
                </button>
            </div>
        </form>
    </vm-section-body>
</vm-section>

<vm-section>
    <vm-section-title translate>reports.list.listTitle</vm-section-title>
    <vm-section-body>
        <table datatable dt-options="table.options" dt-columns="table.columns" dt-instance="table.instance"
                class="table table-striped table-condensed table-hover"></table>
    </vm-section-body>
</vm-section>
