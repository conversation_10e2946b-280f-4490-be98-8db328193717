(function() {
    'use strict';

    /**
     * Module for transactions viewing.
     */
    angular.module('ovv.transactions', ['ovv.common.tables'])
        .config(transactionsConfig);

    /* @ngInject */
    function transactionsConfig($stateProvider) {
        $stateProvider
            .state('app.transactions', {
                abstract: true,
                url: '/transactions',
                template: '<ui-view/>'
            })
            .state('app.transactions.list', {
                url: '/',
                templateUrl: 'app/modules/transactions/transaction-list.html',
                controller: 'TransactionListController'
            });
    }
})();
