    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"
                ng-click="close()">&times;</button>
        <h4 class="modal-title" data-translate="batch.detail.title">OVV detail</h4>
    </div>
    <div class="modal-body">
        <div class="row" ng-if="!transaction.id">
            <div class="col-md-12">
                <h4 data-translate="batch.detail.notExistTransaction" data-translate-values='{ id: {{transactionId}}}'></h4>
            </div>
        </div>
        <div class="row" ng-if="transaction.id">
            <dl class="col-md-6">
                <dt data-translate="transaction.id"></dt>
                <dd>{{transaction.id}}</dd>
                <dt data-translate="transaction.serverDateTime"></dt>
                <dd>{{transaction.serverDateTime | date:'d.M.yyyy, HH:mm:ss'}}</dd>
                <dt data-translate="transaction.deviceDateTime"></dt>
                <dd>{{transaction.deviceDateTime | date:'d.M.yyyy, HH:mm:ss'}}</dd>
                <dt data-translate="transaction.countryCode"></dt>
                <dd>{{transaction.countryCode}}</dd>
                <dt data-translate="transaction.partnerId"></dt>
                <dd>{{transaction.partnerId}}</dd>
                <dt data-translate="transaction.paymentPlace"></dt>
                <dd>{{transaction.paymentPlace}}</dd>
                <dt data-translate="transaction.deviceId"></dt>
                <dd>{{transaction.deviceId}}</dd>
                <dt data-translate="transaction.manualRedemption"></dt>
                <dd data-translate="common.filter.{{transaction.manualRedemption ? 'YES':'NO'}}"></dd>
                <dt data-translate="transaction.stan"></dt>
                <dd>{{transaction.stan}}</dd>
            </dl>
            <dl class="col-md-6">
                <dt data-translate="transaction.type"></dt>
                <dd data-translate="transaction.type.{{transaction.transactionType}}"></dd>
                <dt data-translate="transaction.amount"></dt>
                <dd>{{transaction.amount/100 | number : (transaction.amount % 100 != 0) ? 2 : 0}}</dd>
                <dt data-translate="transaction.voucherNumber"></dt>
                <dd>{{transaction.voucherNumber}}</dd>
                <dt data-translate="transaction.ovvIssuer"></dt>
                <dd>{{transaction.ovvIssuer}}</dd>
                <dt data-translate="transaction.validationHost"></dt>
                <dd>{{transaction.validationHost}}</dd>
                <dt data-translate="transaction.category"></dt>
                <dd ng-show="transaction.category != null" data-translate="transaction.category.{{transaction.category}}"><span ng-class="getLabel(transaction.category)"></span></dd>
                <dt data-translate="transaction.trainingMode"></dt>
                <dd data-translate="common.filter.{{transaction.trainingMode ? 'YES':'NO'}}"></dd>
                <dt data-translate="transaction.offlineMode"></dt>
                <dd data-translate="common.filter.{{transaction.offlineMode ? 'YES':'NO'}}"></dd>
                <dt data-translate="transaction.resultCode"></dt>
                <dd data-translate="transaction.resultCode.{{transaction.resultCode}}"><span ng-class="getLabel(transaction.resultCode)"></span></dd>
                <dt data-translate="transaction.errorDescription"></dt>
                <dd>{{transaction.errorDescription ? transaction.errorDescription : ""}}</dd>
            </dl>
        </div>

    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal" ng-click="close()">
            <span data-translate="batch.detail.close">Close</span>
        </button>
    </div>
