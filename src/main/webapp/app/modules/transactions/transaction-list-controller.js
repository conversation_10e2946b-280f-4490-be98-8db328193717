(function() {
    'use strict';

    angular.module('ovv.transactions')
        .controller('TransactionListController', TransactionListController);

    /* @ngInject */
    function TransactionListController($scope, $filter, TableAjax, $translate, StoreResource, $rootScope, $httpParamSerializer, $modal, TransactionResource, ConfigService) {

      $scope.searchTimePeriod = ConfigService.getParams().transactionSearchTimeLimit;

        $scope.countryCodes = [ 'CZ'];
        $scope.transactionTypes = [
            { value: 'VALIDATION', title: 'transaction.type.VALIDATION' },
            { value: 'REVERSAL', title: 'transaction.type.REVERSAL' }
        ];

        $scope.trainingModes = [
            { value: 'ALL', title: $filter('translate')('common.filter.ALL') },
            { value: 'YES', title: $filter('translate')('common.filter.YES') },
            { value: 'NO', title: $filter('translate')('common.filter.NO') }
        ];

        $scope.offlineModes = [
            { value: 'ALL', title: $filter('translate')('common.filter.ALL') },
            { value: 'YES', title: $filter('translate')('common.filter.YES') },
            { value: 'NO', title: $filter('translate')('common.filter.NO') }
        ];

        $scope.manualRedemptions = [
            { value: 'ALL', title: $filter('translate')('common.filter.ALL') },
            { value: 'YES', title: $filter('translate')('common.filter.YES') },
            { value: 'NO', title: $filter('translate')('common.filter.NO') }
        ];

        $scope.issuers = [
            'EDENRED',
            'LCHD',
            'SODEXO'
        ];
        $scope.resultCodes = [
            { value: 'APPROVED', title: 'transaction.resultCode.APPROVED' },
            { value: 'ALREADY_USED', title: 'transaction.resultCode.ALREADY_USED' },
            { value: 'MESSAGE_FORMAT_ERROR', title: 'transaction.resultCode.MESSAGE_FORMAT_ERROR' },
            { value: 'WRONG_REVERSAL', title: 'transaction.resultCode.WRONG_REVERSAL' },
            { value: 'VOUCHER_NOT_FOUND', title: 'transaction.resultCode.VOUCHER_NOT_FOUND' },
            { value: 'ISSUER_NOT_FOUND', title: 'transaction.resultCode.ISSUER_NOT_FOUND' },
            { value: 'TIMEOUT', title: 'transaction.resultCode.TIMEOUT' },
            { value: 'WRONG_REPEATED_REQUEST', title: 'transaction.resultCode.WRONG_REPEATED_REQUEST' },
            { value: 'WRONG_VOUCHER_STATE', title: 'transaction.resultCode.WRONG_VOUCHER_STATE' },
            { value: 'WRONG_STORE', title: 'transaction.resultCode.WRONG_STORE' },
            { value: 'DECLINED', title: 'transaction.resultCode.DECLINED' },
            { value: 'CANNOT_PROCESS', title: 'transaction.resultCode.CANNOT_PROCESS' },
            { value: 'SCAN_AGAIN', title: 'transaction.resultCode.SCAN_AGAIN' },
            { value: 'STOLEN', title: 'transaction.resultCode.STOLEN' },
            { value: 'ALREADY_USED_SAME_TRN', title: 'transaction.resultCode.ALREADY_USED_SAME_TRN' },
            { value: 'AFTER_VALIDITY', title: 'transaction.resultCode.AFTER_VALIDITY' },
            { value: 'AMOUNT_DEF_NOT_FOUND', title: 'transaction.resultCode.AMOUNT_DEF_NOT_FOUND' },
            { value: 'NOT_ACCEPTED', title: 'transaction.resultCode.NOT_ACCEPTED' }
        ];

        $scope.account = $rootScope.account;

        $rootScope.$watch('account', function (acc) {
            if (acc) {
                $scope.account = acc;
                if ($scope.account.role !== "ADMIN") {
                    $scope.filter.store = $scope.account.store;
                    $scope.stores = [$scope.account.store];
                }
                if ($scope.table.instance.reloadData) {
                    $scope.table.instance.reloadData();
                }
            }
        });

        var minusDays = function(date, daysCount) {
            return new Date(date.setDate(date.getDate() - daysCount));
        };

        /**
         * Search filter.
         */
        $scope.filter = {};

        if ($scope.account) {
            $scope.filter.store = $scope.account.store;
            $scope.stores = [$scope.account.store];
            $scope.filter.countryCode = $scope.account.countryCode;
        }
        $scope.filter.trainingMode = $scope.trainingModes[2];

        $scope.filter.offlineMode = $scope.offlineModes[0];

        $scope.filter.manualRedemption = $scope.manualRedemptions[0];

        /**
         * Main search function invoked via the filter form.
         */
        $scope.searchTransactions = function() {
            $scope.table.instance.reloadData();
        };

        $scope.refreshStores = function (store) {
            if (!store) {
                store = '';
            }

            return StoreResource.find({text: store, countryCode: $scope.filter.countryCode ,searchScope: 'ALL'}).$promise.then(function (res) {
                $scope.stores = res;
            });
        };
        $scope.datetimepickerOptionsFrom = {
                format: 'D.M.YYYY, HH:mm',
                useCurrent: 'day'
        };

        $scope.datetimepickerOptionsTo = {
                format: 'D.M.YYYY, HH:mm',
                useCurrent: false,
                defaultDate: moment().endOf('day')
        };

        $scope.expirationYearDatetimepickerOptions = {
            viewMode: 'years',
            format: 'YYYY',
            maxDate: moment().add(7, 'years'),
            minDate: moment().add(-3, 'years')
        };

        $scope.clearFilter = function() {
            $scope.filter = {};
            if ($scope.account.role !== "ADMIN") {
                $scope.filter.store = $scope.account.store;
                $scope.stores = [$scope.account.store];
            }
            $scope.filter.countryCode = $scope.account.countryCode;
            $scope.refreshStores();
            $scope.filter.trainingMode = $scope.trainingModes[2];
            $scope.filter.offlineMode = $scope.offlineModes[0];
            $scope.filter.manualRedemption = $scope.manualRedemptions[0];

            var dateTo = moment().endOf('day').format('D.M.YYYY, HH:mm');
            $("#dateTimeTo").data("DateTimePicker").date(dateTo);
            $scope.filter.dateTimeTo = null;

        };



        var toIsoFormat = function(datetime, sourceFormat) {
            if (!datetime) {
                return null;
            }
            return moment(datetime, 'D.M.YYYY, HH:mm').format('YYYY-MM-DDTHH:mm:ss');
        };

        /**
         * Get object with search query parameters of the used filter.
         */
        var getSearchParams = function() {
            var trainingMode = null;
            if ($scope.filter.trainingMode.value == 'YES') {
                trainingMode = true;
            }
            if ($scope.filter.trainingMode.value == 'NO') {
                trainingMode = false;
            }
            var offlineMode = null;
            if ($scope.filter.offlineMode.value == 'YES') {
                offlineMode = true;
            }
            if ($scope.filter.offlineMode.value == 'NO') {
                offlineMode = false;
            }
            var manualRedemption = null;
            if ($scope.filter.manualRedemption.value == 'YES') {
                manualRedemption = true;
            }
            if ($scope.filter.manualRedemption.value == 'NO') {
                manualRedemption = false;
            }
            var resultCodes = [];
            if ($scope.filter.resultCode && $scope.filter.resultCode.length > 0) {
                $scope.filter.resultCode.forEach(function (rc) {
                    resultCodes.push(rc.value);
                });
            }
            var issuers = [];
            if ($scope.filter.issuer && $scope.filter.issuer.length > 0) {
                $scope.filter.issuer.forEach(function (issuer) {
                    issuers.push(issuer);
                });
            }
            return {
                dateTimeFrom: toIsoFormat($scope.filter.dateTimeFrom),
                dateTimeTo: ($scope.filter.dateTimeTo?toIsoFormat(moment($scope.filter.dateTimeTo, 'D.M.YYYY, HH:mm').add(1,'minutes')):null),
                countries: $scope.filter.countryCode ? [$scope.filter.countryCode] : undefined,
                deviceId: $scope.filter.deviceId,
                stan: $scope.filter.stan,
                type: $scope.filter.type ? $scope.filter.type.value : null,
                voucherNumber: $scope.filter.voucherNumber,
                issuer: issuers,
                resultCode: resultCodes,
                siteCodes: $scope.filter.store ? [$scope.filter.store.siteCode] : undefined,
                expirationYear: $scope.filter.expirationYear,
                trainingMode: trainingMode,
                offlineMode: offlineMode,
                manualRedemption: manualRedemption
            };
        };
        $scope.exportParams = function() {
            return $httpParamSerializer(getSearchParams());
        };

        $scope.showDetail = function (id) {
            $modal.open({
                templateUrl: 'app/modules/transactions/transaction-detail.html',
                controller: 'TransactionDetailController',
                // controllerAs: 'vm',
                size: 'lg',
                backdrop: 'static', // resolve problem with clicking outside of modal
                resolve: {
                    transaction: function () {
                        return TransactionResource.get({id: id}).$promise.then(function (response) {
                            return response;
                        }, function (error) {
                            return null;
                        });
                    },
                    transactionId: function () {
                        return id;
                    }
                }
            });
        };

        /**
         * Main table with list of transactions.
         */
        $scope.table = new TableAjax('api/transactions/', getSearchParams, $scope)
                // Transaction Date
                    .addColumn({
                        data: 'serverDateTime',
                        title: $translate('transaction.serverDateTime'),
                        render: function(date) {
                            return $filter('date')(date, 'd.M.yyyy, HH:mm:ss');
                        }
                    })
                    .addColumn({
                        data: 'deviceDateTime',
                        title: $translate('transaction.deviceDateTime'),
                        render: function(date) {
                        return $filter('date')(date, 'd.M.yyyy, HH:mm:ss');
                        }
                    })
                    // Country Code
                    .addColumn({
                        data: 'countryCode',
                        title: $translate('transaction.countryCode'),
                        orderable: false
                    })
                    // Device ID
                    .addColumn({
                        data: 'deviceId',
                        title: $translate('transaction.deviceId'),
                        orderable: false
                    })
                    // Manual redemption
                    .addColumn({
                        data: function(row){
                            return row.manualRedemption ? "YES" : "NO";
                        },
                        render: function(code) {
                            return $filter('translate')('common.filter.' + code);
                        },
                        title: $translate('transaction.manualRedemption'),
                        orderable: false
                    })
                    // STAN
                    .addColumn({
                        data: 'stan',
                        title: $translate('transaction.stan'),
                        orderable: false
                    })
                    // Payment Place
                    .addColumn({
                        data: 'paymentPlace',
                        title: $translate('transaction.paymentPlace'),
                        orderable: false
                    })
                    // Transaction Type
                    .addColumn({
                        data: 'transactionType',
                        title: $translate('transaction.type'),
                        render: function(type) {
                            return $filter('translate')('transaction.type.' + type);
                        },
                        orderable: false
                    })
                    // Amount
                    .addColumn({
                        data: function(row) {
                            var fractions = row.amount % 100 != 0;
                            var amount = row.amount;
                            if (row.transactionType === 'REVERSAL' && amount > 0) {
                                amount = -(amount);
                            }
                            return $filter('number')(amount / 100, fractions ? 2 : 0);
                        },
                        title: $translate('transaction.amount'),
                        className: 'text-center text-nowrap',
                        orderable: false
                    })
                    // S/N
                    .addColumn({
                        data: 'voucherNumber',
                        title: $translate('transaction.voucherNumber')
                    })
                    // Issuer
                    .addColumn({
                        data: 'ovvIssuer',
                        title: $translate('transaction.ovvIssuer'),
                        orderable: false
                    })
                    // Offline
                    .addColumn({
                        data: function(row){
                                return row.offlineMode ? "YES" : "NO";
                        },
                        render: function(code) {
                            return $filter('translate')('common.filter.' + code);
                        },
                        title: $translate('transaction.offlineMode'),
                        orderable: false
                    })
                    // training mode
                    .addColumn({
                        data: function(row){
                            return row.trainingMode ? "YES" : "NO";
                        },
                        render: function(code) {
                            return $filter('translate')('common.filter.' + code);
                        },
                        title: $translate('transaction.trainingMode'),
                        orderable: false
                    })
                    // Result Code
                    .addColumn({
                        data: 'resultCode',
                        title: $translate('transaction.resultCode'),
                        render: function(code) {
                            return '<span class="label">' + $filter('translate')('transaction.resultCode.' + code) + '</span>';
                        },
                        createdCell: function(cell, cellData, rowData, rowIndex, colIndex) {
                            var labelClass;
                            switch (rowData.resultCode) {
                                case 'APPROVED':
                                    labelClass = 'label-success';
                                    break;
                                case 'MESSAGE_FORMAT_ERROR':
                                case 'ISSUER_NOT_FOUND':
                                case 'TIMEOUT':
                                case 'WRONG_STORE':
                                case 'CANNOT_PROCESS':
                                case 'WRONG_VOUCHER_STATE':
                                    labelClass = 'label-warning';
                                    break;
                                default:
                                    labelClass = 'label-danger';
                            }
                            angular.element(cell).find('span').addClass(labelClass);
                        },
                        orderable: false
                    })
                    // Detail
                    .addColumn({
                        data: 'id',
                        title: $translate('transaction.detail'),
                        render: function (data, type, full, meta) {
                            return '<a ng-click="showDetail(' + data + ')\" ' +
                                + 'data-tooltip="' + $filter('translate')('transaction.detail') + '">'
                                + '<i class="fa fa-fw fa-info"></i></a>';
                        },
                        orderable: false
                    })
                    .compileRows($scope)
                    .sort([[0, 'desc']]);


        $scope.fromAfterBefore = function() {
            var from = $scope.filter.dateTimeFrom;
            var to = $scope.filter.dateTimeTo;
            var datetimeFrom = moment(from, 'D.M.YYYY');
            var datetimeTo = moment(to, 'D.M.YYYY');
            return datetimeFrom.isAfter(datetimeTo);
        };


    }
})();
