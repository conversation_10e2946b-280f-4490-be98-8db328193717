(function() {
    'use strict';

    angular.module('ovv.transactions')
        .controller('TransactionDetailController', TransactionDetailController);

    /* @ngInject */
    function TransactionDetailController($scope, transaction, transactionId, $modalInstance) {

        $scope.transaction = transaction;
        $scope.transactionId = transactionId;

        $scope.close = function () {
            $modalInstance.close();
        };

        $scope.getLabel = function (code) {
            switch (code) {
                case 'APPROVED':
                    return 'label label-success';
                    break;
                case 'MESSAGE_FORMAT_ERROR':
                case 'ISSUER_NOT_FOUND':
                case 'TIMEOUT':
                case 'WRONG_STORE':
                case 'CANNOT_PROCESS':
                    return 'label label-warning';
                    break;
                default:
                    return 'label label-danger';
            }
        };
    }
})();
