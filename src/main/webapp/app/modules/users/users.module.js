(function() {
    'use strict';

    /**
     * Module for user administration.
     */
    angular.module('ovv.users', ['ovv.common.tables', 'ovv.common.validation'])
        .config(usersConfig);

    /* @ngInject */
    function usersConfig($stateProvider) {
        $stateProvider
            .state('app.users', {
                abstract: true,
                url: '/users',
                template: '<ui-view/>'
            })
            .state('app.users.list', {
                url: '/',
                templateUrl: 'app/modules/users/user-list.html',
                controller: 'UserListController'
            })
            .state('app.users.new', {
                url: '/new',
                templateUrl: 'app/modules/users/user-form.html',
                controller: 'UserNewController'
            })
            .state('app.users.edit', {
                url: '/:id/edit',
                templateUrl: 'app/modules/users/user-form.html',
                controller: 'UserEditController',
                resolve: {
                    user: ['$stateParams', 'UserResource', function($stateParams, UserResource) {
                        return UserResource.get({ userId: $stateParams.id }).$promise;
                    }]
                }
            });
    }
})();
