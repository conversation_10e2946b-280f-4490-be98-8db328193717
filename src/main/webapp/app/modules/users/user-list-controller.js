(function() {
    'use strict';

    angular.module('ovv.users')
        .controller('UserListController', UserListController);

    /* @ngInject */
    function UserListController($scope, $rootScope, $filter, $http, TableAjax, ovvEvents) {
        $scope.roles = [
            { value: 'ADMIN', title: $filter('translate')('user.role.ADMIN') },
            { value: 'VIEWER', title: $filter('translate')('user.role.VIEWER') },
            { value: 'MANUAL_REDEMPTION', title: $filter('translate')('user.role.MANUAL_REDEMPTION') },
            { value: 'BATCH', title: $filter('translate')('user.role.BATCH') },
            { value: 'STORE_MANAGER', title: $filter('translate')('user.role.STORE_MANAGER') }

        ];
        $scope.activeFilterItems = [
            { value: 'ALL', title: $filter('translate')('common.filter.ALL') },
            { value: 'YES', title: $filter('translate')('common.filter.YES') },
            { value: 'NO', title: $filter('translate')('common.filter.NO') }
        ];

        /**
         * Search filter.
         */
        $scope.filter = { active: $scope.activeFilterItems[1] };

        $scope.roleFilter = function(element) {
            if ($scope.account.role == 'STORE_MANAGER' ) {
                return (!(element.value === 'BATCH') && !(element.value === 'VIEWER') && !(element.value === 'MANUAL_REDEMPTION')) ? false : true;
            }
            else {
                return true;
            }
        };


        $scope.users = [];
        $scope.account = $rootScope.account;

        /**
         * Main search function invoked via the filter form.
         */
        $scope.searchUsers = function() {
            if ($scope.filter.active == undefined) {
                // set active to yes
                $scope.filter = { active: $scope.activeFilterItems[1] };
            }
            $scope.table.instance.reloadData();
        };

        $scope.clearFilter = function() {
            $scope.filter = { active: $scope.activeFilterItems[1] };
        };

        /**
         * Get object with search query parameters of the used filter.
         */
        var getSearchParams = function() {
            var params = {
                username: $scope.filter.username,
                name: $scope.filter.name,
                role: $scope.filter.role ? $scope.filter.role.value : null
            };
            if ($scope.filter.active.value != 'ALL') {
                params.active = $scope.filter.active.value == 'YES';
            }
            return params;
        };

        var getActionRowButtons = function(user) {
            var actionCell = '<div><div>';
            // Edit link
            actionCell += '<a data-ui-sref="app.users.edit({ id: ' + user.id + '})" '
                + 'data-tooltip="' + $filter('translate')('administration.users.editUser') + '">'
                + '<i class="fa fa-fw fa-pencil"></i></a>';
            if (user.active) {
                // Disable button
                actionCell += '<a href="#" ng-click="disableUser(users[' + user.id + '])" '
                    + 'data-tooltip="' + $filter('translate')('administration.users.disableUser') + '">'
                    + '<i class="fa fa-fw fa-ban"></i></a>';
            } else {
                // Enable button
                actionCell += '<a href="#" ng-click="enableUser(users[' + user.id + '])" '
                    + 'data-tooltip="' + $filter('translate')('administration.users.enableUser') + '">'
                    + '<i class="fa fa-fw fa-thumbs-up"></i></a>';
            }
            actionCell += '</div></div>';
            return actionCell;
        };

        /**
         * Main table with list of transactions.
         */
        $scope.table = new TableAjax('api/users/', getSearchParams, $scope)
            // Username
            .addColumn({
                data: 'username',
                title: $filter('translate')('user.username')
            })
            // First Name
            .addColumn({
                data: 'firstName',
                title: $filter('translate')('user.firstName')
            })
            // Last Name
            .addColumn({
                data: 'lastName',
                title: $filter('translate')('user.lastName')
            })
            // E-mail
            .addColumn({
                data: 'email',
                title: $filter('translate')('user.email')
            })
            .addColumn({
                data: 'role',
                title: $filter('translate')('user.role'),
                render: function(role) {
                    return $filter('translate')('user.role.' + role);
                },
                orderable: false
            })
            .addColumn({
                data: function(row) {
                    return getActionRowButtons(row);
                },
                orderable: false,
                title: ''
            })
            .compileRows($scope)
            .addCreatedRowCallback(function(row, data, index) {
                $scope.users[data.id] = data;
            })
            .sort([[0, 'asc']]);

        $scope.disableUser = function(user) {
            $http.post('api/users/' + user.id + '/changeStatus', null, {
                params: { active: false }
            }).then(function(response) {
                $rootScope.$broadcast(ovvEvents.SUCCESS_MSG,
                        $filter('translate')('administration.users.disableUser.success', user));
                $scope.searchUsers();
            });
        };

        $scope.enableUser = function(user) {
            $http.post('api/users/' + user.id + '/changeStatus', null, {
                params: { active: true }
            }).then(function(response) {
                $rootScope.$broadcast(ovvEvents.SUCCESS_MSG,
                        $filter('translate')('administration.users.enableUser.success', user));
                $scope.searchUsers();
            });
        };
    }
})();
