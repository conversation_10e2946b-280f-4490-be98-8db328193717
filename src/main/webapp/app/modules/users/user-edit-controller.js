(function() {
    'use strict';

    angular.module('ovv.users')
        .controller('UserEditController', UserEditController);

    /* @ngInject */
    function UserEditController($scope, $rootScope, $state, $filter, ovvEvents, validationErrors, user, StoreResource) {
        $scope.roles = [
            { value: 'ADMIN', title: 'user.role.ADMIN' },
            { value: 'VIEWER', title: 'user.role.VIEWER' },
            { value: 'MANUAL_REDEMPTION', title: 'user.role.MANUAL_REDEMPTION' },
            { value: 'BATCH', title: 'user.role.BATCH' },
            { value: 'STORE_MANAGER', title: 'user.role.STORE_MANAGER' }
        ];

        $scope.countries = [
            { value: 'CZ', title: 'country.CZ' },
//            { value: 'PL', title: 'country.PL' },
//            { value: 'SK', title: 'country.SK' },
//            { value: 'HU', title: 'country.HU' }
        ];

        $scope.account = $rootScope.account;

        $scope.roleFilter = function(element) {
            if ($scope.account.role == 'STORE_MANAGER' ) {
                return (!(element.value === 'BATCH') && !(element.value === 'VIEWER') && !(element.value === 'MANUAL_REDEMPTION')) ? false : true;
            }
            else {
                return true;
            }
        };

        $scope.user = user;
        if (user.role === 'ADMIN') {
            $scope.user.selectedRole = $scope.roles[0];
        } else if (user.role === 'VIEWER') {
            $scope.user.selectedRole = $scope.roles[1];
        } else if (user.role === 'MANUAL_REDEMPTION') {
            $scope.user.selectedRole = $scope.roles[2];
        } else if (user.role === 'BATCH') {
            $scope.user.selectedRole = $scope.roles[3];
        } else if (user.role === 'STORE_MANAGER') {
            $scope.user.selectedRole = $scope.roles[4];
        }

        if (user.countryCode === 'CZ') {
            $scope.user.selectedCountry = $scope.countries[0];
        } else if (user.countryCode === 'PL') {
            $scope.user.selectedCountry = $scope.countries[1];
        } else if (user.countryCode === 'SK') {
            $scope.user.selectedCountry = $scope.countries[2];
        } else if (user.countryCode === 'HU') {
            $scope.user.selectedCountry = $scope.countries[3];
        }

        if (user.store) {
            $scope.user.selectedStore = user.store;
        }
        /**
         * Handler of form submit button.
         */
        $scope.submit = function(userForm) {
            $scope.messages.clear();
            if (userForm.$invalid) {
                return;
            }
            $scope.user.role = $scope.user.selectedRole.value;
            $scope.user.store = $scope.user.selectedStore;
            $scope.user.countryCode = ($scope.user.selectedCountry) ? $scope.user.selectedCountry.value : null;
            $scope.user.$update(
                function(value) {
                    $state.go('app.users.list').then(function() {
                        $rootScope.$broadcast(ovvEvents.SUCCESS_MSG,
                                $filter('translate')('administration.users.info.updated'));
                        if($scope.account.role == 'ADMIN' && $scope.account.username ==  $scope.user.username){
                            //update logged user info if user updated himself
                            $rootScope.account.countryCode = $scope.user.countryCode;
                        }
                    });
                },
                validationErrors.process()
            );
        };

        $scope.clearStores = function () {
            $scope.user.stores = [];
       };

        $scope.refreshStores = function (store) {
            if (!store) {
                store = '';
            }

            return StoreResource.find({text: store, countryCode: ($scope.user.selectedCountry?$scope.user.selectedCountry.value:'')}).$promise.then(function (res) {
                $scope.storesList = res;
            });
        };
    }
})();
