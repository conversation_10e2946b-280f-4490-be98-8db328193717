'use strict';

angular.module('ovv.users')

.service('PasswordChangeDialogService', ['ngDialog', function(ngDialog) {
    this.show = function(user, asAdmin) {
        return ngDialog.open({
            template: 'app/modules/users/user-password-change.html',
            controller: 'UserPasswordChangeController',
            width: 600,
            className: 'vm-dialog',
            data: {
                user: user,
                asAdmin: asAdmin
            }
        }).closePromise;
    }
}])

.controller('UserPasswordChangeController', ['$scope', '$rootScope', '$filter', '$http', 'ngDialog',
                                             'ovvEvents', 'validationErrors',
    function($scope, $rootScope, $filter, $http, ngDialog, ovvEvents, validationErrors) {
        $scope.user = $scope.ngDialogData.user;
        $scope.oldPasswordRequired = !$scope.ngDialogData.asAdmin;

        $scope.oldPassword = null;
        $scope.newPassword = null;
        $scope.newPasswordConfirm = null;

        /**
         * Handler of form submit.
         */
        $scope.submit = function(form) {
            if (form.$invalid) {
                return;
            }

            $http.post('api/users/' + $scope.user.id + '/changePassword', {
                'oldPassword': $scope.oldPassword,
                'newPassword': $scope.newPassword,
                'newPasswordConfirm': $scope.newPasswordConfirm
            }).then(function(response) {
                // Success
                $rootScope.$broadcast(ovvEvents.SUCCESS_MSG, $filter('translate')('administration.users.changePassword.success', {'username': $scope.user.username}));
            }, validationErrors.process()).then(function() {
                ngDialog.close();
            });
        };
    }]);
