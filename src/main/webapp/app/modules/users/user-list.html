<h1 class="page-title" translate>administration.users.list.heading</h1>

<vm-messages messages="messages"></vm-messages>

<div class="btn-toolbar">
    <a class="btn btn-primary btn-sm width-150" data-ui-sref="app.users.new">
        <i class="fa fa-plus">&nbsp;</i>
        {{ 'administration.users.list.addUser' | translate }}
    </a>
</div>

<section class="widget vm-filter">
    <div class="widget-body">
        <form>
            <div class="row">
                <div class="col-sm-4 col-md-3 form-group">
                    <label for="username" translate>user.username</label>
                    <input type="text" id="username" name="username" class="form-control"
                            ng-model="filter.username">
                </div>
                <div class="col-sm-4 col-md-3 form-group">
                    <label for="name" translate>user.name</label>
                    <input type="text" id="name" name="name" class="form-control"
                            ng-model="filter.name">
                </div>
                <div class="col-sm-4 col-md-3 form-group">
                    <label for="role" translate>user.role</label>
                    <ui-select id="role" name="role" class="form-control"
                            ng-model="filter.role" search-enabled="false" >
                        <ui-select-match allow-clear="true">
                            {{$select.selected.title}}
                        </ui-select-match>
                        <ui-select-choices repeat="item in roles | filter: roleFilter">
                            {{item.title}}
                        </ui-select-choices>
                    </ui-select>
                </div>
                <div class="col-sm-4 col-md-3 form-group">
                    <label for="active" translate>user.active</label>
                    <ui-select id="active" name="active" class="form-control"
                            ng-model="filter.active" search-enabled="false" >
                        <ui-select-match allow-clear="true">
                            {{$select.selected.title}}
                        </ui-select-match>
                        <ui-select-choices repeat="item in activeFilterItems">
                            {{item.title}}
                        </ui-select-choices>
                    </ui-select>
                </div>
            </div>
            <div class="btn-toolbar">
                <button type="submit" class="btn btn-sm btn-primary" ng-click="searchUsers()">
                    <i class="fa fa-search mr-xs"></i>
                    <span translate>common.filter.search</span>
                </button>
                <button type="reset" class="btn btn-sm btn-gray" ng-click="clearFilter()">
                    <i class="fa fa-times text-danger mr-xs"></i>
                    <span translate>common.filter.reset</span>
                </button>
            </div>
        </form>
    </div>
</section>

<section class="widget">
    <div class="widget-body">
        <table datatable dt-options="table.options" dt-columns="table.columns" dt-instance="table.instance"
                class="table table-striped table-condensed table-hover"></table>
    </div>
</section>
