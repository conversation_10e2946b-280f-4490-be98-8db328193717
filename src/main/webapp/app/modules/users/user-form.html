<h1 class="page-title" ng-if="newUser" translate>administration.users.add.heading</h1>
<h1 class="page-title" ng-if="!newUser" translate>administration.users.edit.heading</h1>

<vm-messages messages="messages"></vm-messages>

<section class="widget">
    <header>
        <h5>
            <span ng-if="newUser" translate>administration.users.add.title</span>
            <span ng-if="!newUser">{{ 'administration.users.edit.title' | translate: { 'username': user.username } }}</span>
        </h5>
    </header>
    <div class="widget-body">
        <form class="form-horizontal" name="form" novalidate>
            <div class="row">
                <div class="col-md-6">
                    <fieldset>
                        <div class="form-group">
                            <label for="username" class="col-sm-4 control-label" translate>user.username</label>
                            <div class="col-sm-7">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-user"></i></span>
                                    <input type="text" id="username" name="username" class="form-control"
                                           ng-model="user.username" ng-disabled="!newUser" ng-required="newUser" ng-maxlength="64" autofocus>
                                </div>
                                <vm-input-errors field="form.username" required maxlength />
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="firstName" class="col-sm-4 control-label" translate>user.firstName</label>
                            <div class="col-sm-7">
                                <input type="text" id="firstName" name="firstName" class="form-control"
                                       ng-model="user.firstName" ng-maxlength="64">
                                <vm-input-errors field="form.firstName" maxlength />
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="lastName" class="col-sm-4 control-label" translate>user.lastName</label>
                            <div class="col-sm-7">
                                <input type="text" id="lastName" name="lastName" class="form-control"
                                       ng-model="user.lastName" ng-maxlength="64">
                                <vm-input-errors field="form.lastName" maxlength />
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="email" class="col-sm-4 control-label" translate>user.email</label>
                            <div class="col-sm-7">
                                <input type="email" id="email" name="email" class="form-control"
                                       ng-model="user.email" ng-maxlength="64">
                                <vm-input-errors field="form.email" maxlength email="This is not a valid email." />
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="role" class="col-sm-4 control-label" translate>user.role</label>
                            <div class="col-sm-7">
                                <ui-select id="role" name="role" class="form-control"
                                        ng-model="user.selectedRole" search-enabled="true" required>
                                    <ui-select-match>
                                        {{$select.selected.title | translate}}
                                    </ui-select-match>
                                    <ui-select-choices repeat="item in roles | filter: roleFilter | filter : $select.search">
                                        <div ng-bind-html="item.title | highlight: $select.search | translate"></div>
                                    </ui-select-choices>
                                </ui-select>
                                <vm-input-errors field="form.role" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="country" class="col-sm-4 control-label" translate>user.countryCode</label>
                              <div class="col-sm-7">
                                <ui-select id="country" name="country" class="form-control" on-select="clearStores();refreshStores($select.search)"
                                        ng-model="user.selectedCountry" search-enabled="true"
                                        ng-required="!(user.selectedRole.value == 'ADMIN')"
                                        ng-disabled="!(account.role == 'ADMIN')">
                                    <ui-select-match allow-clear="true">
                                        {{$select.selected.title | translate}}
                                    </ui-select-match>
                                    <ui-select-choices repeat="item in countries | filter: roleFilter | filter : $select.search">
                                        <div ng-bind-html="item.title | highlight: $select.search | translate"></div>
                                    </ui-select-choices>
                                </ui-select>
                                <vm-input-errors field="form.country" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="store" class="col-sm-4 control-label" translate>user.store</label>
                            <div class="col-sm-7">
                                <ui-select multiple id="store" name="store" class="form-control"
                                    ng-disabled="!(user.selectedRole.value == 'ADMIN') && !user.selectedCountry.value"
                                           ng-model="user.stores" search-enabled="true" ng-required="!(user.selectedRole.value == 'ADMIN')">
                                    <ui-select-match allow-clear="true">
                                         {{$item.siteCode}} - {{$item.name}}
                                    </ui-select-match>
                                    <ui-select-choices repeat="store in storesList track by $index"
                                                       refresh="refreshStores($select.search)"
                                                       refresh-delay="2">
                                        {{store.siteCode + ' ' + store.name}}
                                    </ui-select-choices>
                                </ui-select>
                                <vm-input-errors field="form.store" required />
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="col-md-6">
                    <fieldset>
                        <div class="form-group">
                            <label for="active" class="col-sm-4 control-label" translate>user.active</label>
                            <div class="col-sm-7">
                                <div class="checkbox checkbox-primary">
                                    <input id="active" type="checkbox" checked ng-model="user.active">
                                    <label for="active">&nbsp;</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="password" class="col-sm-4 control-label" translate>user.password</label>
                            <div class="col-sm-7">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-lock"></i></span>
                                    <input type="password" id="password" name="password" class="form-control"
                                           ng-model="user.password" ng-required="newUser" ng-minlength="8">
                                </div>
                                <vm-input-errors field="form.password" required minlength="form.password.error.tooShort" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="passwordConfirm" class="col-sm-4 control-label" translate>user.passwordConfirm</label>
                            <div class="col-sm-7">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-lock"></i></span>
                                    <input type="password" id="passwordConfirm" name="passwordConfirm" class="form-control"
                                           ng-model="user.passwordConfirm" ng-required="newUser" vm-match="user.password">
                                </div>
                                <vm-input-errors field="form.passwordConfirm" required match="form.password.error.match" />
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>
            <div class="form-actions btn-toolbar text-align-center">
                <button type="submit" class="btn btn-primary" ng-click="submit(form)" translate>form.save</button>
                <a type="button" class="btn btn-inverse" data-ui-sref="app.users.list" translate>form.cancel</a>
            </div>
        </form>
    </div>
</section>
