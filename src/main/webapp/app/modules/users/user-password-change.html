<h3 class="page-title" translate>users.changePassword.heading</h3>

<section class="widget">
    <header>
        <!--
        <h5>{{ 'users.changePassword.title' | translate: user }}</h5>
        -->
    </header>
    <div class="widget-body">
        <form class="form-horizontal" name="form" novalidate>
            <div class="form-group" ng-if="oldPasswordRequired">
                <label for="oldPassword" class="col-sm-4 control-label" translate>users.changePassword.oldPassword</label>
                <div class="col-sm-7">
                    <div class="input-group">
                        <span class="input-group-addon"><i class="fa fa-lock"></i></span>
                        <input type="password" id="oldPassword" name="oldPassword" class="form-control"
                               ng-model="$parent.oldPassword" required>
                    </div>
                    <vm-input-errors field="form.oldPassword" required />
                </div>
            </div>
            <div class="form-group">
                <label for="password" class="col-sm-4 control-label" translate>users.changePassword.newPassword</label>
                <div class="col-sm-7">
                    <div class="input-group">
                        <span class="input-group-addon"><i class="fa fa-lock"></i></span>
                        <input type="password" id="password" name="password" class="form-control"
                               ng-model="newPassword" required ng-minlength="8" vm-password>
                    </div>
                    <vm-input-errors field="form.password" required
                            minlength="form.password.error.tooShort"
                            passworddigit="form.password.error.digits"
                            passwordlowercase="form.password.error.lowercase"
                            passworduppercase="form.password.error.uppercase" />
                </div>
            </div>
            <div class="form-group">
                <label for="passwordConfirm" class="col-sm-4 control-label" translate>users.changePassword.confirmNewPassword</label>
                <div class="col-sm-7">
                    <div class="input-group">
                        <span class="input-group-addon"><i class="fa fa-lock"></i></span>
                        <input type="password" id="passwordConfirm" name="passwordConfirm" class="form-control"
                               ng-model="newPasswordConfirm" required vm-match="newPassword">
                    </div>
                    <vm-input-errors field="form.passwordConfirm" required match="form.password.error.match" />
                </div>
            </div>
            <div class="form-actions text-align-center">
                <button type="submit" class="btn btn-primary" ng-click="submit(form)" translate>form.save</button>
                <button type="button" class="btn btn-inverse" ng-click="closeThisDialog()" translate>form.cancel</button>
            </div>
        </form>
    </div>
</section>
