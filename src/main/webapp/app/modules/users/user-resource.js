(function() {
    'use strict';

    angular.module('ovv.users')
        .factory('UserResource', UserResource);

    /* @ngInject */
    function UserResource($resource) {
        var url = './api/users';

        return $resource(url + '/:userId', { userId: '@id' }, {
            update: { url: url + '/:userId', method: 'PUT' },
            getLoggedUser: {url: url + '/getLoggedUser', method: 'GET' }
        });

    }
})();
