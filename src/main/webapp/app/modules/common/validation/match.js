(function() {
    'use strict';

    angular.module('ovv.common.validation')
        .directive('vmMatch', vmMatch);

    function vmMatch($parse) {
        return {
            require: 'ngModel',
            scope: {
                primaryValue: "=vmMatch"
            },
            link: function(scope, elm, attrs, ctrl) {
                ctrl.$validators.match = function(modelValue, viewValue) {
                    if (!scope.primaryValue) {
                        return true;
                    }
                    return modelValue === scope.primaryValue;
                };
                scope.$watch("primaryValue", function() {
                    ctrl.$validate();
                });
            }
        };
    }
})();
