(function() {
    'use strict';

    angular.module('ovv.common.validation')
        .service('validationErrors', validationErrors);

    /* @ngInject */
    function validationErrors($filter, $rootScope, ovvEvents) {
        this.process = function(msgConfigs) {
            // Prepare message configurations
            var msgMap = {};
            angular.forEach(msgConfigs, function(config) {
                var field = null;
                if (config.field) {
                    field = config.field;
                }
                if (!msgMap[field]) {
                    msgMap[field] = {};
                }
                if (config.msgKey) {
                    msgMap[field][config.code] = $filter('translate')(config.msgKey);
                }
            });

            var getErrorMessage = function(error) {
                if (!error.code) {
                    // Error code not defined => invalid error object
                    return null;
                }
                if (!msgMap[error.field] || !msgMap[error.field][error.code]) {
                    // No specific mapping found, return the original error message text
                    return error.message;
                }
                return msgMap[error.field][error.code];
            };

            // Return error response callback
            return function(response) {
                if (response.status !== 400) {
                    // Process only HTTP 400 "Bad Request" responses
                    return;
                }

                var msgDisplayed = false;
                angular.forEach(response.data, function(error) {
                    var errorMsg = getErrorMessage(error);
                    if (errorMsg) {
                        msgDisplayed = true;
                        $rootScope.$broadcast(ovvEvents.ERROR_MSG, errorMsg);
                    }
                });
                if (!msgDisplayed) {
                    // Emit a default 'invalid request' message
                    $rootScope.$broadcast(ovvEvents.ERROR_MSG, $filter('translate')('common.error.invalidRequest'));
                }
            };
        };
    }
})();
