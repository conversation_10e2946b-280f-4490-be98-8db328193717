(function() {
    'use strict';

    angular.module('ovv.cash-registers',[])
        .factory('CashRegisterResource', CashRegisterResource);

    /* @ngInject */
    function CashRegisterResource($resource) {
        var url = './api/cashRegisters';

        return $resource(url + '/:cashRegisterId', { storeId: '@id' }, {
            find: { url: url + '/find', method: 'GET', isArray:true }
        });

    }
})();
