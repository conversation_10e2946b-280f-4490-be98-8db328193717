(function() {
    'use strict';

    angular.module('ovv.common.tables')
        .factory('TableAjax', TableAjax);

    /**
     * Service factory providing TableAjax objects wrapping
     * configuration of a DataTable loaded via configured AJAX requests.
     * The factory returns a constructor function which can be newed.
     *
     * @ngInject
     */
    function TableAjax($compile, $rootScope, ovvEvents, DTOptionsBuilder, tableMessages, $translate, $filter) {
        var TableAjax = function(resourceUrl, fnQueryParams, $scope, type) {
            // DataTable options used by 'dt-options' attribute
            this.options = this.initOptions(resourceUrl, fnQueryParams, $scope, type);
            // DataTable columns used by 'dt-columns' attribute
            this.columns = [];
            // DataTable instance used by 'dt-instance' attribute
            this.instance = {};
            // Callback functions called when a new table row is created
            this.createdRowCallbacks = [];
        };
        /**
         * Initialize DataTables options with a common server-side request processing configuration.
         *
         * @param resourceUrl Target resource URL.
         * @param fnQueryParams Function returning object with query parameters to be sent to the resource URL.
         * @param $scope mandatory variable, scope of source controller
         * @param type type of table
         * @return {*}
         */
        TableAjax.prototype.initOptions = function(resourceUrl, fnQueryParams, $scope, type) {
            return DTOptionsBuilder.newOptions()
                    .withOption('ajax', {
                        url: resourceUrl,
                        data: function(origData) {
                            var params = {
                                draw: origData.draw,
                                length: origData.length,
                                start: origData.start,
                                orderKey: origData.order.length > 0 ? origData.columns[origData.order[0].column].data : null,
                                orderDir: origData.order.length > 0 ? origData.order[0].dir : null
                            };
                            return angular.extend(params, fnQueryParams());
                        },
                        error: function(xhr, error, thrown) {
                            if (xhr.status == 401) {
                                $rootScope.$broadcast(ovvEvents.UNAUTHORIZED_ERROR, xhr);
                            } else {
                                $rootScope.$broadcast(ovvEvents.COMM_ERROR, xhr);
                            }
                        },
                        traditional: true
                    })
                    .withDataProp('data')
                    .withOption('serverSide', true)
                    .withOption('processing', true)
                    .withOption('paging', true)
                    .withOption('info', true)
                    .withOption('searching', false)
                    .withOption('lengthChange', true)
                    .withOption('autoWidth', false)
                    .withBootstrap()
                    .withOption('createdRow', angular.bind(this, function(row, data, dataIndex) {
                        if (type === 'batchFile') {
                            if (data.reconciliated) {
                                row.setAttribute('class', 'success');
                            } else {
                                row.setAttribute('class', 'danger');
                            }
                        }
                        // Call all registered createdRow callbacks
                        angular.forEach(this.createdRowCallbacks, function(callback) {
                            callback(row, data, dataIndex);
                        });
                    }))
                    .withLanguage(tableMessages)
                    .withPaginationType('full')
                    .withDisplayLength(25)
                    .withOption('lengthMenu', [[10, 25, 50],[10, 25, 50]])
                    .withOption('bFilter', false)
                    //.withDOM('<"top pull-left itemtableInfo"i>rt<"bottom"p>')
                    .withLanguage({
                        "info": $filter('translate')('datatables.show') || "Showing _START_ to _END_",
                        "infoEmpty": $filter('translate')('datatables.show.empty') || "Nothing to show",
                        "emptyTable": $filter('translate')('datatables.show.empty') || "Nothing to show",
                        "processing": $filter('translate')('datatables.processing') || "Processing...",
                        "loadingRecords": "...",
                        "paginate": {
                            "first": false,
                            "last": false,
                            "next": $filter('translate')('datatables.paginate.next') || "Next",
                            "previous": $filter('translate')('datatables.paginate.previous') || "Previous"
                        }

                    });
        };

        TableAjax.prototype.compileRows = function($scope) {
            this.createdRowCallbacks.push(function(row, data, dataIndex) {
                $compile(angular.element(row))($scope);
            });
            return this;
        };

        TableAjax.prototype.addCreatedRowCallback = function(rowCallback) {
            this.createdRowCallbacks.push(rowCallback);
            return this;
        };

        TableAjax.prototype.addColumn = function(column) {
            this.columns.push(column);
            return this;
        };

        TableAjax.prototype.sort = function(order) {
            this.options.withOption('order', order);
            return this;
        };

        TableAjax.prototype.reloadData = function() {
            this.instance.reloadData();
        };

        return TableAjax;
    }
})();
