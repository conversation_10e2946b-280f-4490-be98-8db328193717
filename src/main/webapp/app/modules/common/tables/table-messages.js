(function() {
    'use strict';

    angular.module('ovv.common.tables')
        .factory('tableMessages', tableMessages);

    /* @ngInject */
    function tableMessages($filter) {
        var $trans = $filter('translate');
        return {
            sEmptyTable: $trans('datatables.noData'),
            sInfo: $trans('datatables.show'),
            sInfoEmpty: $trans('datatables.show.empty'),
            sLengthMenu: $trans('datatables.lengthMenu'),
            oPaginate: {
                sNext: $trans('datatables.paginate.next'),
                sPrevious: $trans('datatables.paginate.previous')
            },
            sProcessing: $trans('datatables.processing')
        };
    }
})();
