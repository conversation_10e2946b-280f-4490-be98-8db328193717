(function() {
  'use strict';

  angular.module('singApp.core.utils', [])
    .directive('inputGroupNoBorder', inputGroupNoBorder)
    .directive('ajaxLoad', ajaxLoad)
    .directive('checkAll', checkAll)
    .directive('body', body)
  ;

  /* ========================================================================
   * Handle transparent input groups focus
   * ========================================================================
   */
  inputGroupNoBorder.$inject = ['jQuery'];
  function inputGroupNoBorder(jQuery){
    return {
      restrict: 'C',
      link: function (scope, el){
        jQuery(el).find('.input-group-addon + .form-control').on('blur focus', function(e){
          jQuery(this).parents('.input-group')[e.type==='focus' ? 'addClass' : 'removeClass']('focus');
        });
      }
    }
  }

  /* ========================================================================
   * Ajax Load micro-plugin
   * ========================================================================
   */

  ajaxLoad.$inject = ['jQuery', '$window'];
  function ajaxLoad(jQuery, $window){
    return {
      restrict: 'A',
      link: function(scope, $el, attrs){
        $el.on('click change', function(e){
          var $this = jQuery(this),
            $target = jQuery($this.data('ajax-target'));
          if ($target.length > 0 ){
            e = jQuery.Event('ajax-load:start', {originalEvent: e});
            $this.trigger(e);

            !e.isDefaultPrevented() && $target.load($this.data('ajax-load'), function(){
              $this.trigger('ajax-load:end');
            });
          }
          return false;
        });

        /**
         * Change to loading state if loading text present
         */
        if (attrs.loadingText){
          $el.on('ajax-load:start', function () {
            $el.button('loading');
          });
          $el.on('ajax-load:end', function () {
            $el.button('reset');
          });
        }

        jQuery($window.document).on('click', '[data-toggle^=button]', function (e) {
          return jQuery(e.target).find('input').data('ajax-trigger') !== 'change';
        });
      }
    }
  }

  checkAll.$inject = ['jQuery'];
  function checkAll(jQuery){
    return {
      restrict: 'A',
      link: function (scope, $el){
        $el.on('click', function() {
          $el.closest('table').find('input[type=checkbox]')
            .not(this).prop('checked', jQuery(this).prop('checked'));
        });
      }
    }
  }

  body.$inject = [];
  function body () {
    return {
      restrict: 'E',
      link: function(scope, $element) {
        // prevent unwanted navigation
        $element.on('click', 'a[href=#]', function(e) {
          e.preventDefault();
        })
      }
    }
  }

})();
