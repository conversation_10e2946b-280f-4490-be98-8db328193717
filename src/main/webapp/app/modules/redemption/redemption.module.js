(function() {
    'use strict';

    /**
     * Module for transactions viewing.
     */
    angular.module('ovv.redemption', [])
        .config(transactionsConfig);

    /* @ngInject */
    function transactionsConfig($stateProvider) {
        $stateProvider
            .state('app.redemption', {
                abstract: true,
                url: '/redemption',
                template: '<ui-view/>'
            })
            .state('app.redemption.create', {
                url: '/',
                templateUrl: 'app/modules/redemption/redemption.html',
                controller: 'RedemptionController'
            });
    }
})();
