<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true" ng-click="close()">&times;</button>
    <h4 class="modal-title" data-translate="redemption.setAmount.heading">OVV detail</h4>
</div>
<div class="modal-body">
    <p translate ng-if="!(req.amountConfirmOnly)">redemption.setAmount.info</p>

    <section class="widget mb-0">
        <div class="widget-body">
            <form name="form" novalidate>
                <div>
                    <dl>
                        <dt data-translate="redemption.transactionType"></dt>
                        <dd data-translate="transaction.type.{{req.transactionType}}"/>
                        <dt data-translate="redemption.voucherNumber"></dt>
                        <dd>{{::req.voucherNumber}}</dd>
                        <dt data-translate="redemption.store"></dt>
                        <dd>{{::req.store.siteCode}} - {{::req.store.name}}</dd>
                        <dt data-translate="redemption.redemptionDateTime"></dt>
                        <dd>{{::req.redemptionDate}}</dd>
                        <dt data-translate="redemption.amount"></dt>
                        <dd style="width: 50%">
                            <input type="text" id="amount" name="amount" class="form-control"
                                   ng-disabled="req.amountConfirmOnly" ng-model="req.amount" ng-maxlength="25"
                                   ng-minlength="1" pattern="[0-9]+([,][0-9]{1,2})?" size="25" required>
                            <vm-input-errors field="form.amount" required pattern="form.number.error.invalid"
                                             maxlength/>
                        </dd>
                    </dl>
                </div>
                <div class="form-actions btn-toolbar text-align-center">
                    <button type="submit" class="btn btn-primary" ng-click="form.$valid && submit()" translate>
                        redemption.confirmAmount
                    </button>
                    <button type="button" class="btn btn-inverse" ng-click="close()" translate>common.cancel</button>
                </div>
            </form>
        </div>
    </section>
</div>
