(function() {
    'use strict';

    angular
        .module('ovv-viewer', [
            'singApp.core',
            'pascalprecht.translate',
            'ui.select',
            'datetimepicker',
            'ngSanitize',
            'ngMessages',
            'ngResource',
            'ngDialog',
            'cgBusy',
            'ovv.comp.messages',
            'ovv.comp.input-errors',
            'ovv.transactions',
            'ovv.users',
            'ovv.stores',
            'ovv.redemption',
            'ovv.batch',
            'ui.bootstrap',
            'ovv.configuration',
            'ovv.utils',
            'ovv.cash-registers',
            'ovv.reports'
        ])
        .constant('homeState', 'app.transactions.list')
        .constant('ovvEvents', {
            COMM_ERROR: 'CommError',
            UNAUTHORIZED_ERROR: 'UnauthorizedError',
            ERROR_MSG: 'ErrorMsg',
            SUCCESS_MSG: 'SuccessMsg',
            WARNING_MSG: 'WarningMsg',
            CLEAR_MSGS: 'ClearMsgs'
        })
        .config(urlRouterProviderConfig)
        .config(resourceProviderConfig)
        .config(selectConfig)
        .config(datetimepickerProviderConfig)
        .config(tooltipProviderConfig)
        .config(translateProviderConfig)
        .config(httpProviderConfig)
        .config(stateProviderConfig)
        .factory('errorResponseInterceptor', errorResponseInterceptor)
        .controller('AppController', AppController)
        .run(init);

    /* @ngInject */
    function urlRouterProviderConfig($urlRouterProvider, homeState) {
        // Set up default states based on the logged user
        $urlRouterProvider.otherwise(function($injector, $location) {
            var path = $location.path();
            if (path) {
                // Do nothing
                return;
            }
            // Handle empty default path, redirect to proper page
            var $state = $injector.get('$state');
            $state.go(homeState);
        });
    }

    /* @ngInject */
    function resourceProviderConfig($resourceProvider) {
        // Don't strip trailing slashes from calculated URLs
        $resourceProvider.defaults.stripTrailingSlashes = false;
    }

    /* @ngInject */
    function selectConfig(uiSelectConfig) {
        uiSelectConfig.theme = 'select2';
    }

    /* @ngInject */
    function datetimepickerProviderConfig(datetimepickerProvider) {
        datetimepickerProvider.setOptions({
            showClear: true,
            allowInputToggle: true
        });
    }

    /* @ngInject */
    function tooltipProviderConfig($tooltipProvider) {
        $tooltipProvider.options({ popupDelay: 500 });
    }

    /* @ngInject */
    function translateProviderConfig($translateProvider) {
        $translateProvider
            .useUrlLoader('./messages')
            .preferredLanguage($translateProvider.resolveClientLocale())
            .useSanitizeValueStrategy('escapeParameters');
    }

    /* @ngInject */
    function httpProviderConfig($httpProvider) {
        $httpProvider.interceptors.push('errorResponseInterceptor');
    }

    /**
     * Configuration of root state.
     *
     * @ngInject
     */
    function stateProviderConfig($stateProvider) {
        $stateProvider
            .state('app', {
                url: '',
                abstract: true,
                templateUrl: 'app/modules/_sing/core/app.html',
              resolve: {
                  configParams: ['ConfigService', function(ConfigService) {
                      return ConfigService.load();
                  }]
                }
            });
    }

    /**
     * Factory of custom error HTTP interceptor. Handles HTTP 401 Unauthorized responses
     * by redirecting to the login page.
     *
     * @ngInject
     */
    function errorResponseInterceptor($q, $rootScope, ovvEvents) {
        return {
            'responseError': function(response) {
                if (response.status == 401) {
                    $rootScope.$broadcast(ovvEvents.UNAUTHORIZED_ERROR, response);
                } else if (response.status != 400) {
                    $rootScope.$broadcast(ovvEvents.COMM_ERROR, response);
                }
                return $q.reject(response);
            }
        };
    }

    /**
     * Main application controller, attached to <body>.
     *
     * @ngInject
     */
    function AppController(config, $scope, $localStorage, $state, $rootScope, $filter, $window, ovvEvents, Messages, UserResource, PasswordChangeDialogService) {
        // Stuff from original Sing 'App' controller (core.js)
        this.title = config.title;

        $scope.app = config;
        $scope.$state = $state;
        $scope.busy = false;

        if (angular.isDefined($localStorage.state)) {
            $scope.app.state = $localStorage.state;
        } else {
            $localStorage.state = $scope.app.state;
        }

        UserResource.getLoggedUser().$promise.then(function (res) {
            $rootScope.account = res;
            $scope.user = res;
        });

        $scope.messages = new Messages();

        // Clear all global messages upon successful state change
        $scope.$on('$stateChangeSuccess', function(event, toState, toParams, fromState, fromParams) {
            $scope.messages.clear();
        });

        $rootScope.$on('busy', function(event, msg) {
            $scope.busy = msg;
        });

        // Listen for new error messages
        $scope.$on(ovvEvents.ERROR_MSG, function(event, msg) {
            $scope.messages.error(msg);
        });
        // Listen for new success messages
        $scope.$on(ovvEvents.SUCCESS_MSG, function(event, msg) {
            $scope.messages.success(msg);
        });
        // Listen for new warning messages
        $scope.$on(ovvEvents.WARNING_MSG, function(event, msg) {
            $scope.messages.warning(msg);
        });
        // Listen for request to clear all messages
        $scope.$on(ovvEvents.CLEAR_MSGS, function(event, response) {
            $scope.messages.clear();
        });

        $scope.$on(ovvEvents.COMM_ERROR, function(event, response) {
            // TODO doplnit
            console.error('CHYBA');
            console.error(response);
//            ErrorDialog.show(response);
        });

        $scope.$on(ovvEvents.UNAUTHORIZED_ERROR, function(event, response) {
            var path = $window.location.pathname;
            $window.location.replace(path.substring(0, path.indexOf('/', 2)) + '/login?expired=true');
        });

        $scope.passwordChange = function() {
            PasswordChangeDialogService.show($scope.user, false);
        };
    }

    /* @ngInject */
    function init($localStorage) {
        delete $localStorage.dashboardVouchers;
    }
})();
