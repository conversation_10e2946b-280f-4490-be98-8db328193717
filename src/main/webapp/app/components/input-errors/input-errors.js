'use strict';

angular.module('ovv.comp.input-errors', ['ngMessages'])

.directive('vmInputErrors', function() {
    return {
        restrict: 'E',
        require: '^form',
        scope: {
            field: '='
        },
        template: '<div ng-messages="field.$error" ng-if="form.$submitted || field.$dirty"\
                ng-messages-multiple class="vm-input-errors"></div>',

        compile: function(tElement, tAttrs) {
            var tDiv = tElement.find('div');
            // Add well known messages
            if (tAttrs.required !== undefined) {
                tDiv.append('<span ng-message="required" translate>form.field.error.required</span>');
            }
            if (tAttrs.maxlength !== undefined) {
                tDiv.append('<span ng-message="maxlength" translate>form.field.error.tooLong</span>');
            }
            if (tAttrs.invalid !== undefined) {
                tDiv.append('<span ng-message="invalid" translate>form.number.error.invalid</span>');
                tDiv.append('<span ng-message="max" translate>form.number.error.invalid</span>');
                tDiv.append('<span ng-message="min" translate>form.number.error.invalid</span>');
            }

            // Add additional messages defined in attributes
            for (var attrName in tAttrs) {
                if (attrName === 'field' || attrName.charAt(0) === '$') {
                    // Skip 'field' and reserved attributes starting with '$'
                    continue;
                }
                var attr = tAttrs[attrName];
                if (typeof attr == 'string' && attr.length) {
                    // Non-empty string attribute => add as ngMessage
                    tDiv.append('<span ng-message="' + attrName + '" translate>' + attr + '</span>');
                }
            }

            return {
                post: function($scope, $element, attrs, formController) {
                    $scope.form = formController;
                }
            };
        }
    };
});
