<div class="alert alert-sm alert-dismissible" ng-class="getMsgClass(message)"
        ng-repeat="message in messages.data track by $index">
    <button type="button" class="close" ng-click="dismissMsg(message)">
        <span aria-hidden="true">&times;</span>
    </button>
    <span ng-switch="message.type">
        <span class="fw-semi-bold" ng-switch-when="INFO" translate>comp.messages.info</span>
        <span class="fw-semi-bold" ng-switch-when="SUCCESS" translate>comp.messages.success</span>
        <span class="fw-semi-bold" ng-switch-when="WARN" translate>comp.messages.warning</span>
        <span class="fw-semi-bold" ng-switch-when="ERROR" translate>comp.messages.error</span>
    </span>
    {{::message.content}}
</div>
