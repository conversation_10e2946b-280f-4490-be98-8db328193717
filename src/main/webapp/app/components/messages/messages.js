'use strict';

angular.module('ovv.comp.messages', [])

.factory('Messages', function() {
    var Messages = function() {
        this.data = [];
    };
    Messages.prototype.info = function(msg) {
        this.data.push({ type: 'INFO', content: msg });
    };
    Messages.prototype.success = function(msg) {
        this.data.push({ type: 'SUCCESS', content: msg });
    };
    Messages.prototype.warning = function(msg) {
        this.data.push({ type: 'WARN', content: msg });
    };
    Messages.prototype.error = function(msg) {
        this.data.push({ type: 'ERROR', content: msg });
    };
    Messages.prototype.clear = function() {
        this.data.splice(0);
    };
    Messages.prototype.isEmpty = function() {
        return this.data.length === 0;
    }
    return Messages;
})

.directive('vmMessages', function() {
    return {
        restrict: 'E',
        scope: {
            messages: '='
        },
        templateUrl: 'app/components/messages/messages.html',
        link: function($scope, $elem, $attrs) {
            $scope.getMsgClass = function(message) {
                switch (message.type) {
                case 'INFO':
                    return 'alert-info';
                case 'SUCCESS':
                    return 'alert-success';
                case 'WARN':
                    return 'alert-warning';
                case 'ERROR':
                    return 'alert-danger';
                default:
                    return '';
                }
            };

            $scope.dismissMsg = function(message) {
                var index = $scope.messages.data.indexOf(message);
                if (index >= 0) {
                    $scope.messages.data.splice(index, 1);
                }
            };
        }
    };
});
