'use strict';

angular.module('ovv.utils', [])

.service('DateUtils', ['ConfigService','$filter', function(ConfigService, $filter) {
    /**
     * Re-format the date in custom configured format to the ISO local date format (e.g. 2016-07-24).
     */
    this.toISOLocalDate = function(date) {
        if (!date) {
            return null;
        }
        // Expecting formatted date on input
        return moment(date, ConfigService.getParams().dateFormat).format('YYYY-MM-DD');
    };

    this.toLocalDateString = function(isoDateString) {
        if (!isoDateString) {
            return null;
        }
        // Expecting formatted date on input
        return $filter('date')(isoDateString, 'd.M.yyyy');
    };

    this.toISOLocalDateObject = function(date) {
        if (!date) {
            return null;
        }
        // Expecting formatted date on input
        return moment(date, ConfigService.getParams().dateFormat).toDate();
    };
}]);
