'use strict';

angular.module('ovv.configuration', [])

/**
 * Service for access to configuration parameters.
 */
.service('ConfigService', ['$http', function($http) {
    this._loadPromise = null;
    this._configParams = null;

    /**
     * Load configuration parameters from server. Return promise.
     */
    this.load = function() {
        if (!this._loadPromise) {
            // Not requested yet
            this._loadPromise = $http.get('configuration').then(angular.bind(this, function(response) {
                this._configParams = response.data;
                return this._configParams;
            }));
        }
        return this._loadPromise;
    };

    /**
     * Get loaded configuration parameters.
     */
    this.getParams = function() {
        return this._configParams;
    };
}]);
