create table REPORT
(
    id                 bigserial                   not null,
    created_date       timestamp without time zone not null,
    data               oid,
    modified_date      timestamp without time zone not null,
    params             character varying(255),
    status             varchar(16)                 not null,
    type               varchar(32)                 not null,
    created_by_user_id bigint                      not null,
    store_id           bigint,
    primary key (id)
);

alter table REPORT
    add constraint FK_REPORT_CREATED_BY_USER_ID
        foreign key (created_by_user_id)
            references ovv_user;

alter table REPORT
    add constraint FK_REPORT_STORE_ID
        foreign key (store_id)
            references ovv_store;

create table VOUCHER
(
    id                             bigint                 not null,
    country_code                   character varying(16)  NOT NULL,
    server_local_date_time         timestamp              not null,
    voucher_number                 character varying(255) not null,
    voucher_state                  character varying(32)  not null,
    related_transaction_request_id bigint,
    expiration_date_time           timestamp,
    primary key (id)
);

CREATE INDEX ix_VOUCHER_server_time ON voucher (server_local_date_time);
CREATE INDEX ix_VOUCHER_related_request ON voucher (related_transaction_request_id);
ALTER TABLE REPORT OWNER to ovv;
ALTER TABLE VOUCHER OWNER to ovv;