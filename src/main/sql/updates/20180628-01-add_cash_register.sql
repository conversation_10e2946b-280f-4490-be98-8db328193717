create table ovv_cash_register (
  id bigserial not null,
  number varchar(255) not null,
  site_code varchar(255) not null,
  active boolean NOT NULL,
  import_date timestamp without time zone NOT NULL,
  primary key (id)
);

ALTER TABLE ovv_cash_register ADD CONSTRAINT uk_ovv_cash_register_number_sitecode_active UNIQUE (number, site_code, active);
CREATE INDEX ix_ovv_cash_register ON ovv_cash_register (site_code);


