create table OVV_STORE (
  id bigint not null,
  cost_centre varchar(255) not null,
  country_code varchar(2) not null,
  name varchar(255) not null,
  site_code varchar(255) not null,
  partner_id varchar(255) not null,
  primary key (id)
);

ALTER TABLE OVV_STORE ADD CONSTRAINT uk_ovv_store_cc_pi_sc UNIQUE (country_code, partner_id, site_code);
ALTER TABLE ovv_user ADD COLUMN store_id BIGINT;
ALTER TABLE ovv_user ADD CONSTRAINT fk_ovv_store_store_id FOREIGN KEY (store_id) REFERENCES ovv_store;

CREATE SEQUENCE ovv_store_seq START 1;
