CREATE TABLE ovv_user_store (
    id bigserial NOT NULL,
    user_id int8 NOT NULL,
    store_id int8 NOT NULL,
    primary key (id)
);

ALTER TABLE ovv_user_store OWNER to ovv;

ALTER TABLE ovv_user_store ADD CONSTRAINT fk_ovv_store_store_id FOREIGN KEY (store_id) REFERENCES ovv_store;
ALTER TABLE ovv_user_store ADD CONSTRAINT fk_ovv_user_user_id FOREIGN KEY (user_id) REFERENCES ovv_user;

insert into ovv_user_store(user_id, store_id) select u.id as user_id, u.store_id as store_id from ovv_user u where u.store_id is not null;

