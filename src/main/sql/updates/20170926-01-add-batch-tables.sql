    create table REPORT_BATCH (
        id bigint NOT NULL,
        ovv_transaction_request_id bigint,
        result_code smallint,
        result_description character varying(255),
        voucher_number character varying(255),
        report_file_id bigint NOT NULL,
        store_id bigint NOT NULL,
        primary key (id)
    );

    create table REPORT_FILE (
        id bigint NOT NULL,
        country_code character varying(5) not null,
        created timestamp without time zone NOT NULL,
        file_name character varying(255) not null,
        filename_date character varying(255) not null,
        file_path character varying(255) not null,
        items_count integer not null,
        ovv_issuer character varying(255) not null,
        reconciliated  timestamp without time zone,
        uuid bigint not null,
        primary key (id)
    );

