# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a hybrid Java Spring Boot + AngularJS application for Billa's OVV (Online Voucher Validation) viewer system. The backend uses Spring 6.x with Java 17, while the frontend uses AngularJS 1.6.9.

## Key Commands

### Build and Run
```bash
# Full build with tests
mvn clean install

# Build without tests
mvn clean package -DskipTests

# Run locally (requires PostgreSQL)
mvn spring-boot:run

# Build Docker image
docker build . -t ovv-viewer

# Run Docker container
docker run -p80:8080 --name ovv-viewer ovv-viewer
```

### Frontend Development
```bash
cd SingApp/angular/

# Install dependencies (run once)
npm install
bower install

# Build frontend
gulp build

# Development server with live reload
gulp serve

# Run frontend tests
gulp test

# Run tests in watch mode
gulp test:auto
```

### Database Setup
```bash
# Start local PostgreSQL
cd tools/postgre-in-docker-compose/
docker-compose up

# Configure in src/main/resources/jdbc.properties
```

### Testing
```bash
# Run backend tests
mvn test

# Run specific test
mvn test -Dtest=CategoryReportGeneratorTest

# Frontend tests
cd SingApp/angular/
gulp test
```

## Architecture

### Backend Structure
- `src/main/java/cz/wincor/ovv/viewer/`
  - `bootstrap/` - Spring configuration classes
  - `controller/` - REST and MVC controllers
    - `api/` - REST API endpoints
  - `dto/` - Data Transfer Objects
  - `model/` - JPA entities and enums
    - `entity/` - Database entities
    - `search/` - Search criteria classes
  - `repository/` - Spring Data JPA repositories
  - `service/` - Business logic
    - `impl/` - Service implementations
  - `utils/` - Utility classes
  - `validation/` - Custom validators

### Frontend Structure
- `src/main/webapp/app/`
  - `common/` - Shared services and utilities
  - `components/` - Reusable UI components
  - `modules/` - Feature modules
    - `transactions/` - Transaction management
    - `users/` - User management  
    - `stores/` - Store management
    - `batch/` - Batch processing
    - `reports/` - Report generation
    - `redemption/` - Voucher redemption

### Key Technologies
- **Backend**: Spring Boot 3.3.3, Spring Security 6.3.3, Hibernate 6.6.0, PostgreSQL
- **Frontend**: AngularJS 1.6.9, UI Router, Bootstrap 3.3.4, Angular DataTables
- **Build**: Maven 3.x, Gulp 3.9.1, Bower 1.8.2
- **Testing**: JUnit 5, Mockito, Karma/Jasmine

## Development Patterns

### Backend
- Service layer pattern with interfaces and implementations
- Spring Data JPA repositories with QueryDSL for complex queries
- DTO pattern for API responses
- Spring Security for authentication/authorization
- Thymeleaf for server-side rendering (login, error pages)

### Frontend
- Modular AngularJS structure
- UI Router for navigation
- Angular Translate for i18n (CS, HU, PL, SK)
- Lazy loading with ocLazyLoad
- Bootstrap-based responsive design

### API Endpoints
- REST APIs under `/api/*` (requires authentication)
- MVC endpoints for UI pages
- Excel report generation endpoints
- SFTP import processing

### Security
- Spring Security with BCrypt password encoding
- Role-based access control (ADMIN, USER roles)
- CSRF protection enabled
- Session-based authentication