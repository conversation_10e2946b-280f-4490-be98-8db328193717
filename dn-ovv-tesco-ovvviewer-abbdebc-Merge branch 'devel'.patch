From caca9e072e455b0794918d69047a3b536dbb8843 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 1 Aug 2025 13:52:49 +0200
Subject: [PATCH] fix: improve handling of null values in sort and filter logic

- Return Sort.unsorted() instead of null when no sort criteria are provided
- Avoid sending null entries in countries and siteCodes filter arrays by setting them as undefined when not present

Signed-off-by: <PERSON> <<EMAIL>>
---
 .../cz/wincor/ovv/viewer/service/impl/BaseServiceImpl.java    | 2 +-
 .../app/modules/transactions/transaction-list-controller.js   | 4 ++--
 2 files changed, 3 insertions(+), 3 deletions(-)

diff --git a/src/main/java/cz/wincor/ovv/viewer/service/impl/BaseServiceImpl.java b/src/main/java/cz/wincor/ovv/viewer/service/impl/BaseServiceImpl.java
index 7fa34c1..7ba24e7 100644
--- a/src/main/java/cz/wincor/ovv/viewer/service/impl/BaseServiceImpl.java
+++ b/src/main/java/cz/wincor/ovv/viewer/service/impl/BaseServiceImpl.java
@@ -123,7 +123,7 @@ public abstract class BaseServiceImpl {
      */
     protected Sort getSorting(BaseSearchCriteria searchCriteria) {
         if (searchCriteria.getSortBy() == null || searchCriteria.getSortBy().isEmpty()) {
-            return null;
+            return Sort.unsorted();
         }
         return Sort.by(searchCriteria.getDirection(), searchCriteria.getSortBy().toArray(new String[0]));
     }
diff --git a/src/main/webapp/app/modules/transactions/transaction-list-controller.js b/src/main/webapp/app/modules/transactions/transaction-list-controller.js
index 0d8d3e5..6ea6e63 100644
--- a/src/main/webapp/app/modules/transactions/transaction-list-controller.js
+++ b/src/main/webapp/app/modules/transactions/transaction-list-controller.js
@@ -198,14 +198,14 @@
             return {
                 dateTimeFrom: toIsoFormat($scope.filter.dateTimeFrom),
                 dateTimeTo: ($scope.filter.dateTimeTo?toIsoFormat(moment($scope.filter.dateTimeTo, 'D.M.YYYY, HH:mm').add(1,'minutes')):null),
-                countries: [($scope.filter.countryCode) ? $scope.filter.countryCode : null],
+                countries: $scope.filter.countryCode ? [$scope.filter.countryCode] : undefined,
                 deviceId: $scope.filter.deviceId,
                 stan: $scope.filter.stan,
                 type: $scope.filter.type ? $scope.filter.type.value : null,
                 voucherNumber: $scope.filter.voucherNumber,
                 issuer: issuers,
                 resultCode: resultCodes,
-                siteCodes: [$scope.filter.store ? $scope.filter.store.siteCode : null],
+                siteCodes: $scope.filter.store ? [$scope.filter.store.siteCode] : undefined,
                 expirationYear: $scope.filter.expirationYear,
                 trainingMode: trainingMode,
                 offlineMode: offlineMode,
-- 
2.50.1

