# Tool for transfer of TVM users to OVV

### Queries for extracting from TVM
- 8 files (regular users files + admin files) * 4 country

#### regular users

    SELECT vu.EMAIL, vu.FIRST_NAME, vu.LAST_NAME, vu.PASSWORD, vu.USERNAME, vu.USER_ROLE, st.STORE_NUMBER, 'CZ'
    FROM VM_USER vu JOIN STORE st ON vu.STORE_ID = st.ID
    WHERE ACTIVE = '1'
    ORDER BY username;

#### admins
    SELECT vu.EMAIL, vu.FIRST_NAME, vu.LAST_NAME, vu.PASSWORD, vu.USERNAME, vu.USER_ROLE, 'CZ' AS country
    FROM VM_USER vu
    WHERE vu.store_id IS NULL and vu.ACTIVE = '1'
    ORDER BY vu.username;



### Run by executing

    java -jar userTransfer-0.0.1-SNAPSHOT-jar-with-dependencies.jar

with following params


    usage: TransferUserScriptGenerator
     -a,--adminId <arg>   id of user which will be the creator of all imported
                          users
     -i,--input <arg>     input files directory(different from output folder)
     -o,--output <arg>    output file directory(different from input folder)
     -t,--test            testing output

Do not forget to check if there are non-admin users without shop

    select
        ou.username
    from
        ovv_user ou
    where
        ou."role" <> 'ADMIN'
        and not exists(
            select
                *
            from
                ovv_user_store ous
            where
                ou.id = ous.user_id
        )
