package cz.dn.transfer;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.Reader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Iterator;
import java.util.UUID;
import java.util.stream.Stream;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.ParseException;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;

public class TransferUserScriptGenerator {

    private static final String INSERT_OVV_USER_TEMPLATE = "INSERT INTO ovv_user"
            + "(created_date, modified_date, active, email, first_name, last_name, password, \"role\", username, modified_by_user_id, is_account_non_expired, country_code) "
            + "SELECT current_timestamp, current_timestamp, true, '%s', '%s', '%s', '%s', '%s', '%s', %s, false, '%s' WHERE NOT EXISTS("
            + "SELECT id FROM ovv_user WHERE username = '%s');";

    private static final String INSERT_OVV_USER_STORE_TEMPLATE = "INSERT INTO ovv_user_store (user_id, store_id)"
            + " SELECT (SELECT id FROM ovv_user ou WHERE ou.username = '%s'),(SELECT id FROM ovv_store os WHERE os.site_code = '%s') WHERE EXISTS("
            + " SELECT id FROM ovv_store WHERE site_code = '%s');";

    private static boolean isTest = false;

    /**
     * Run with following parameters -o <output folder> -i <folder with input files>
     *
     * @param args
     * @throws IOException
     */
    public static void main(String[] args) throws IOException {

        Options options = new Options();

        Option input = new Option("i", "input", true, "input files directory(different from output folder)");
        input.setRequired(true);
        options.addOption(input);

        Option output = new Option("o", "output", true, "output file directory(different from input folder)");
        output.setRequired(true);
        options.addOption(output);

        Option testOption = Option.builder("t").longOpt("test").hasArg(false).required(false).desc("testing output")
                .build();
        options.addOption(testOption);

        Option adminIdOption = new Option("a", "adminId", true,
                "id of user which will be the creator of all imported users");
        adminIdOption.setRequired(true);
        options.addOption(adminIdOption);

        CommandLineParser parser = new DefaultParser();
        HelpFormatter formatter = new HelpFormatter();
        CommandLine cmd;
        String inputFilePath = null;
        String outputFilePath = null;
        Integer adminId = null;
        try {
            cmd = parser.parse(options, args);
            inputFilePath = cmd.getOptionValue("input");
            outputFilePath = cmd.getOptionValue("output");
            isTest = cmd.hasOption("test");
            adminId = Integer.parseInt(cmd.getOptionValue("adminId"));
        } catch (ParseException e) {
            System.out.println(e.getMessage());
            formatter.printHelp("TransferUserScriptGenerator", options);

            System.exit(1);
        }

        FileWriter fileWriter = new FileWriter(outputFilePath + File.separator + "output.txt");
        PrintWriter printWriter = new PrintWriter(fileWriter);
        process(inputFilePath, adminId, fileWriter, printWriter);
    }

    private static void process(String inputFilePath, Integer adminId, FileWriter fileWriter, PrintWriter printWriter)
            throws IOException {
        try (Stream<Path> paths = Files.walk(Paths.get(inputFilePath))) {
            paths.filter(Files::isRegularFile).forEach(path -> {
                try {
                    TransferUserScriptGenerator.processFile(path, printWriter, adminId);
                } catch (IOException e) {
                    System.err.println("File not found: " + path.toAbsolutePath().toString());
                }
            });
        } finally {
            printWriter.close();
            fileWriter.close();
        }
    }

    private static void processFile(Path path, PrintWriter output, Integer adminId) throws IOException {
        try (Reader reader = Files.newBufferedReader(path)) {
            CsvToBean<TvmUser> csvToBean = new CsvToBeanBuilder<TvmUser>(reader).withType(TvmUser.class)
                    .withIgnoreLeadingWhiteSpace(true).withSeparator(';').build();

            Iterator<TvmUser> csvUserIterator = csvToBean.iterator();

            while (csvUserIterator.hasNext()) {
                TvmUser actual = csvUserIterator.next();

                if (!actual.getStoreNumber().matches("^[a-zA-Z].*$")) { // skip pharmacies and other strange prefixed
                                                                        // stores
                    String username = (isTest ? UUID.randomUUID().toString().replace("-", "") : actual.getUsername());
                    if (username.equals("admin")) {
                        // there is admin for each country we need to differentiate between them to
                        // satisfy unique constraints
                        username = username + actual.getCountry().toLowerCase();
                    }
                    String email = (isTest ? RandomStringUtils.random(10, true, true) : actual.getEmail());
                    String firstName = (isTest ? RandomStringUtils.random(10, true, true) : actual.getFirstName());
                    String lastName = (isTest ? RandomStringUtils.random(10, true, true) : actual.getLastName());
                    String password = (isTest ? "$2a$10$cW1jgr1vMprwnhYLfmbmDeMBDFKxdBVt5bOloOFuYUZ.roLkEeVxa"
                            : actual.getPassword());

                    String viewerRole = convertRole(actual.getRole());
                    output.println(String.format(INSERT_OVV_USER_TEMPLATE, email, firstName, lastName, password,
                            viewerRole, username, adminId, actual.getCountry(), username));
                    if (StringUtils.isNotBlank(actual.getStoreNumber())) {
                        output.println(String.format(INSERT_OVV_USER_STORE_TEMPLATE, username, actual.getStoreNumber(),
                                actual.getStoreNumber()));
                    }
                    System.out.println(String.format("%s;%s;%s;%s;%s;%s", actual.getCountry(), firstName, lastName, username, actual.getStoreNumber(), viewerRole));
                }
            }
        }
    }

    /**
     * Map roles according to this Viewer CSD Manual Redemption Cash Office Manager
     * Manager Admin Administrator Admin Supervisor
     *
     * @param tvmRole
     * @return
     */
    private static String convertRole(String tvmRole) {
        HashMap<String, String> map = new HashMap<>();
        map.put("ADMIN", "ADMIN");
        map.put("SUPERVISOR", "ADMIN");
        map.put("STORE_MANAGER", "STORE_MANAGER");
        map.put("CASH_OFFICE_MANAGER", "MANUAL_REDEMPTION");
        map.put("SERVICE_DESK", "VIEWER");
        return map.get(tvmRole);
    }
}
