package cz.dn.transfer;

import com.opencsv.bean.CsvBindByName;

public class TvmUser {
    @CsvBindByName(column = "EMAIL")
    private String email;
    @CsvBindByName(column = "FIRST_NAME")
    private String firstName;
    @CsvBindByName(column = "LAST_NAME")
    private String lastName;
    @CsvBindByName(column = "PASSWORD", required = true)
    private String password;
    @CsvBindByName(column = "USERNAME", required = true)
    private String username;
    @CsvBindByName(column = "USER_ROLE", required = true)
    private String role;
    @CsvBindByName(column = "STORE_NUMBER")
    private String storeNumber;
    @CsvBindByName(column = "COUNTRY", required = true)
    private String country;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getStoreNumber() {
        return storeNumber;
    }

    public void setStoreNumber(String storeNumber) {
        this.storeNumber = storeNumber;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("TvmUser [email=");
        builder.append(email);
        builder.append(", firstName=");
        builder.append(firstName);
        builder.append(", lastName=");
        builder.append(lastName);
        builder.append(", password=");
        builder.append(password);
        builder.append(", username=");
        builder.append(username);
        builder.append(", role=");
        builder.append(role);
        builder.append(", storeNumber=");
        builder.append(storeNumber);
        builder.append(", country=");
        builder.append(country);
        builder.append("]");
        return builder.toString();
    }



}
