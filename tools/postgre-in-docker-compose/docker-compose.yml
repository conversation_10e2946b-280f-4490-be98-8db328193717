####### Connection ###############################
#  host: localhost                               #
#  port: 5492                                    #
#  user: ovv                                    #
#  pass: pass                                    #
#  database: scaler                              #
#  url: ***************************************  #
##################################################

###### Data ############################
# PostgreSQL data is stored in         #
#                                      #
# Container: /var/lib/postgresql       #
########################################

###### Loging ##########################
# PostgreSQL logs are stored in        #
#                                      #
# Local: ./logs                        #
# Container: /var/log/postgresql       #
########################################

###### Persistent data volume ##########
# It is not possible to mount local directory as a persistent volume as PostgreSQL service is running
# under an unprivileged user and checks folder permissions. Shared volumes are mounted under root.
# If you need persistent data it is possible to configure a Docker volume instead.

version: "3"
services:
  postgresql:
    container_name: postgresql-ovvviewer
    image: postgres:11.2
    environment:
      POSTGRES_USER: ovv
      POSTGRES_PASSWORD: pass
      POSTGRES_DB: ovvviewer
    ports:
      - "5493:5432"
    networks:
      - postgres
    #restart: unless-stopped

###### Defaults PGAdmin ################
#  user: user@localhost        #
#  pass: pass                  #
#  sid:                        #
#  http://localhost:8080       #
################################

  # pgadmin:
  #   container_name: pgadmin
  #   image: dpage/pgadmin4
  #   environment:
  #     PGADMIN_DEFAULT_EMAIL: user@localhost
  #     PGADMIN_DEFAULT_PASSWORD: pass
  #   ports:
  #     - "8080:80"
  #   networks:
  #     - postgres
  #   restart: unless-stopped

networks:
  postgres:
    driver: bridge
